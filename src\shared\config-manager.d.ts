/**
 * 配置管理器
 * 用于管理应用程序配置和设置
 */
import { Configuration, MCPServerConfig } from './types';
export declare class ConfigManager {
    private configPath;
    private config;
    private loaded;
    constructor();
    /**
     * 获取配置文件路径
     */
    private getConfigPath;
    /**
     * 获取默认配置
     */
    private getDefaultConfig;
    /**
     * 加载配置
     */
    load(): Promise<Configuration>;
    /**
     * 保存配置
     */
    save(): Promise<void>;
    /**
     * 获取配置值
     */
    get<T = any>(key: string, defaultValue?: T | null): T;
    /**
     * 设置配置值
     */
    set(key: string, value: any): void;
    /**
     * 删除配置值
     */
    delete(key: string): void;
    /**
     * 获取完整配置
     */
    getAll(): Configuration;
    /**
     * 重置配置为默认值
     */
    reset(): Promise<void>;
    /**
     * 合并配置对象
     */
    private mergeConfig;
    /**
     * 添加最近文件
     */
    addRecentFile(filePath: string): void;
    /**
     * 获取最近文件列表
     */
    getRecentFiles(): string[];
    /**
     * 清空最近文件列表
     */
    clearRecentFiles(): void;
    /**
     * 添加MCP服务器配置
     */
    addMCPServer(serverConfig: MCPServerConfig): void;
    /**
     * 移除MCP服务器配置
     */
    removeMCPServer(serverId: string): void;
    /**
     * 更新MCP服务器配置
     */
    updateMCPServer(serverId: string, updates: Partial<MCPServerConfig>): void;
}
