/**
 * 配置管理器
 * 用于管理应用程序配置和设置
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { Configuration, MCPServerConfig } from './types';

export class ConfigManager {
  private configPath: string;
  private config: Configuration;
  private loaded: boolean = false;

  constructor() {
    this.configPath = this.getConfigPath();
    this.config = this.getDefaultConfig();
  }

  /**
   * 获取配置文件路径
   */
  private getConfigPath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'config.json');
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): Configuration {
    return {
      // 应用设置
      app: {
        version: '1.0.0',
        language: 'zh-CN',
        theme: 'dark',
        autoUpdate: true,
      },

      // 播放器设置
      player: {
        autoplay: false,
        volume: 50,
        muted: false,
        playbackRate: 1.0,
        loop: false,
        subtitles: {
          enabled: false,
          fontSize: 16,
          color: '#ffffff',
          backgroundColor: 'rgba(0,0,0,0.5)',
        },
      },

      // AI聊天设置
      ai: {
        apiUrl: 'http://localhost:3000/api/chat',
        apiKey: '',
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 2048,
        systemPrompt: '你是一个友好的AI助手，可以帮助用户解答问题。',
        chatHistory: {
          enabled: true,
          maxMessages: 100,
        },
      },

      // MCP设置
      mcp: {
        localPort: 3001,
        servers: [
          {
            id: 'local-server',
            name: '本地MCP服务器',
            url: 'ws://localhost:3001',
            enabled: true,
            autoConnect: true,
            auth: {
              type: 'none',
            },
          },
        ],
        timeout: 30000,
        retryAttempts: 3,
        retryDelay: 5000,
      },

      // 网络设置
      network: {
        proxy: {
          enabled: false,
          type: 'http',
          host: '',
          port: 8080,
          username: '',
          password: '',
        },
        timeout: 10000,
        userAgent: 'AI Player/1.0.0',
      },

      // 窗口设置
      window: {
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        x: null,
        y: null,
        maximized: false,
        fullscreen: false,
        alwaysOnTop: false,
      },

      // 快捷键设置
      shortcuts: {
        togglePlay: 'Space',
        volumeUp: 'ArrowUp',
        volumeDown: 'ArrowDown',
        seekForward: 'ArrowRight',
        seekBackward: 'ArrowLeft',
        toggleFullscreen: 'F11',
        toggleChat: 'Ctrl+T',
        openFile: 'Ctrl+O',
        quit: 'Ctrl+Q',
      },

      // 最近文件
      recentFiles: [],

      // 书签
      bookmarks: [],
    };
  }

  /**
   * 加载配置
   */
  async load(): Promise<Configuration> {
    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      const loadedConfig = JSON.parse(configData);

      // 合并默认配置和加载的配置
      this.config = this.mergeConfig(this.getDefaultConfig(), loadedConfig);
      this.loaded = true;

      console.log('配置加载成功:', this.configPath);
      return this.config;
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        // 配置文件不存在，使用默认配置并保存
        console.log('配置文件不存在，创建默认配置');
        await this.save();
        this.loaded = true;
        return this.config;
      } else {
        console.error('加载配置失败:', error);
        throw error;
      }
    }
  }

  /**
   * 保存配置
   */
  async save(): Promise<void> {
    try {
      // 确保配置目录存在
      const configDir = path.dirname(this.configPath);
      await fs.mkdir(configDir, { recursive: true });

      // 保存配置
      const configData = JSON.stringify(this.config, null, 2);
      await fs.writeFile(this.configPath, configData, 'utf8');

      console.log('配置保存成功:', this.configPath);
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取配置值
   */
  get<T = any>(key: string, defaultValue: T | null = null): T {
    const keys = key.split('.');
    let value: any = this.config;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return defaultValue as T;
      }
    }

    return value;
  }

  /**
   * 设置配置值
   */
  set(key: string, value: any): void {
    const keys = key.split('.');
    let target: any = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!target[k] || typeof target[k] !== 'object') {
        target[k] = {};
      }
      target = target[k];
    }

    target[keys[keys.length - 1]] = value;
  }

  /**
   * 删除配置值
   */
  delete(key: string): void {
    const keys = key.split('.');
    let target: any = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!target[k] || typeof target[k] !== 'object') {
        return;
      }
      target = target[k];
    }

    delete target[keys[keys.length - 1]];
  }

  /**
   * 获取完整配置
   */
  getAll(): Configuration {
    return { ...this.config };
  }

  /**
   * 重置配置为默认值
   */
  async reset(): Promise<void> {
    this.config = this.getDefaultConfig();
    await this.save();
    console.log('配置已重置为默认值');
  }

  /**
   * 合并配置对象
   */
  private mergeConfig(defaultConfig: any, userConfig: any): any {
    const result = { ...defaultConfig };

    for (const key in userConfig) {
      if (userConfig.hasOwnProperty(key)) {
        if (
          typeof userConfig[key] === 'object' &&
          userConfig[key] !== null &&
          !Array.isArray(userConfig[key]) &&
          typeof defaultConfig[key] === 'object' &&
          defaultConfig[key] !== null &&
          !Array.isArray(defaultConfig[key])
        ) {
          result[key] = this.mergeConfig(defaultConfig[key], userConfig[key]);
        } else {
          result[key] = userConfig[key];
        }
      }
    }

    return result;
  }

  /**
   * 添加最近文件
   */
  addRecentFile(filePath: string): void {
    const recentFiles = this.get<string[]>('recentFiles', []);

    // 移除已存在的相同文件
    const index = recentFiles.indexOf(filePath);
    if (index > -1) {
      recentFiles.splice(index, 1);
    }

    // 添加到开头
    recentFiles.unshift(filePath);

    // 限制最大数量
    if (recentFiles.length > 10) {
      recentFiles.splice(10);
    }

    this.set('recentFiles', recentFiles);
  }

  /**
   * 获取最近文件列表
   */
  getRecentFiles(): string[] {
    return this.get<string[]>('recentFiles', []);
  }

  /**
   * 清空最近文件列表
   */
  clearRecentFiles(): void {
    this.set('recentFiles', []);
  }

  /**
   * 添加MCP服务器配置
   */
  addMCPServer(serverConfig: MCPServerConfig): void {
    const servers = this.get<MCPServerConfig[]>('mcp.servers', []);
    servers.push(serverConfig);
    this.set('mcp.servers', servers);
  }

  /**
   * 移除MCP服务器配置
   */
  removeMCPServer(serverId: string): void {
    const servers = this.get<MCPServerConfig[]>('mcp.servers', []);
    const filteredServers = servers.filter(server => server.id !== serverId);
    this.set('mcp.servers', filteredServers);
  }

  /**
   * 更新MCP服务器配置
   */
  updateMCPServer(serverId: string, updates: Partial<MCPServerConfig>): void {
    const servers = this.get<MCPServerConfig[]>('mcp.servers', []);
    const serverIndex = servers.findIndex(server => server.id === serverId);

    if (serverIndex > -1) {
      servers[serverIndex] = { ...servers[serverIndex], ...updates };
      this.set('mcp.servers', servers);
    }
  }
}
