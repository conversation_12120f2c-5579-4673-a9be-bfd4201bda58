/* 全局样式重置和基础样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  overflow: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

/* 视频元素样式 */
video {
  outline: none;
}

video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 选择文本样式 */
::selection {
  background-color: #007acc;
  color: white;
}

::-moz-selection {
  background-color: #007acc;
  color: white;
}

/* 禁用拖拽选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 工具提示样式 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-align: center;
  border-radius: 4px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* 加载动画 */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007acc;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .hide-on-small-mobile {
    display: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  ::-webkit-scrollbar-thumb {
    background: #ffffff;
  }
  
  .tooltip .tooltiptext {
    background-color: #000000;
    border: 1px solid #ffffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .fade-in,
  .slide-in-right,
  .loading-spinner {
    animation: none;
  }
  
  .tooltip .tooltiptext {
    transition: none;
  }
}

/* 拖拽调整大小样式 */
.resize-handle {
  position: relative;
  cursor: ew-resize;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: rgba(25, 118, 210, 0.1) !important;
}

.resize-handle.resizing {
  background-color: rgba(25, 118, 210, 0.2) !important;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 1px;
}

.resize-handle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) translateX(-3px);
  width: 2px;
  height: 20px;
  background-color: currentColor;
  opacity: 0.3;
  border-radius: 1px;
}

/* 拖拽时的全局样式 */
body.resizing {
  cursor: ew-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: ew-resize !important;
  user-select: none !important;
}

/* 聊天面板动画 */
.chat-panel-enter {
  transform: translateX(100%);
  opacity: 0;
}

.chat-panel-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.chat-panel-exit {
  transform: translateX(0);
  opacity: 1;
}

.chat-panel-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s ease-in, opacity 0.3s ease-in;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
}
