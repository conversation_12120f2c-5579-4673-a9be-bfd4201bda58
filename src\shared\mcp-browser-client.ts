// 浏览器环境的 MCP 客户端 - 通过代理服务器访问 MCP 功能
import axios from 'axios';

export interface MCPBrowserClientConfig {
  proxyUrl?: string;
  timeout?: number;
}

export interface MCPToolCall {
  name: string;
  arguments: Record<string, any>;
}

export interface MCPToolResult {
  success: boolean;
  result?: any;
  error?: string;
}

export class MCPBrowserClient {
  private proxyUrl: string;
  private timeout: number;
  private filesystemServerStarted: boolean = false;

  constructor(config: MCPBrowserClientConfig = {}) {
    this.proxyUrl = config.proxyUrl || 'http://localhost:3001';
    this.timeout = config.timeout || 10000;
  }

  /**
   * 启动文件系统 MCP 服务器
   */
  async startFilesystemServer(config: {
    allowedDirectories?: string[];
    maxFileSize?: number;
    excludePatterns?: string[];
  } = {}): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        `${this.proxyUrl}/api/mcp/filesystem/start`,
        { config },
        { timeout: this.timeout }
      );

      if (response.data.success) {
        this.filesystemServerStarted = true;
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 停止文件系统 MCP 服务器
   */
  async stopFilesystemServer(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        `${this.proxyUrl}/api/mcp/filesystem/stop`,
        {},
        { timeout: this.timeout }
      );

      if (response.data.success) {
        this.filesystemServerStarted = false;
      }

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 调用文件系统工具
   */
  async callFilesystemTool(toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      if (!this.filesystemServerStarted) {
        // 自动启动服务器
        const startResult = await this.startFilesystemServer();
        if (!startResult.success) {
          return {
            success: false,
            error: `Failed to start filesystem server: ${startResult.error}`
          };
        }
      }

      const response = await axios.post(
        `${this.proxyUrl}/api/mcp/filesystem/call`,
        {
          method: 'tools/call',
          params: {
            name: toolCall.name,
            arguments: toolCall.arguments
          }
        },
        { timeout: this.timeout }
      );

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 获取可用工具列表
   */
  async getAvailableTools(): Promise<{ success: boolean; tools?: any[]; error?: string }> {
    try {
      if (!this.filesystemServerStarted) {
        const startResult = await this.startFilesystemServer();
        if (!startResult.success) {
          return {
            success: false,
            error: `Failed to start filesystem server: ${startResult.error}`
          };
        }
      }

      const response = await axios.post(
        `${this.proxyUrl}/api/mcp/filesystem/call`,
        {
          method: 'tools/list',
          params: {}
        },
        { timeout: this.timeout }
      );

      if (response.data.success) {
        return {
          success: true,
          tools: response.data.result.tools
        };
      } else {
        return response.data;
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 获取服务器状态
   */
  async getServerStatus(): Promise<{ success: boolean; servers?: any; error?: string }> {
    try {
      const response = await axios.get(
        `${this.proxyUrl}/api/mcp/status`,
        { timeout: this.timeout }
      );

      return response.data;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 搜索文件
   */
  async searchFiles(pattern: string, directory?: string, maxResults?: number): Promise<MCPToolResult> {
    return this.callFilesystemTool({
      name: 'search_files',
      arguments: {
        pattern,
        directory: directory || process.cwd(),
        maxResults: maxResults || 100
      }
    });
  }

  /**
   * 读取文件
   */
  async readFile(path: string): Promise<MCPToolResult> {
    return this.callFilesystemTool({
      name: 'read_file',
      arguments: { path }
    });
  }

  /**
   * 列出目录
   */
  async listDirectory(path: string): Promise<MCPToolResult> {
    return this.callFilesystemTool({
      name: 'list_directory',
      arguments: { path }
    });
  }

  /**
   * 写入文件
   */
  async writeFile(path: string, content: string): Promise<MCPToolResult> {
    return this.callFilesystemTool({
      name: 'write_file',
      arguments: { path, content }
    });
  }

  /**
   * 检查是否在浏览器环境
   */
  static isBrowserEnvironment(): boolean {
    return typeof window !== 'undefined' && typeof document !== 'undefined';
  }

  /**
   * 检查代理服务器是否可用
   */
  async checkProxyServer(): Promise<boolean> {
    try {
      const response = await axios.get(
        `${this.proxyUrl}/api/mcp/status`,
        { timeout: 3000 }
      );
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

// 单例实例
export const mcpBrowserClient = new MCPBrowserClient();
