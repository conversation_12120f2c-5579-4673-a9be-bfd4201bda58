// 简单的CORS代理服务器，用于解决浏览器环境中的跨域问题
// 使用方法：node cors-proxy-server.js
// 然后在AI设置中将Base URL设置为 http://localhost:8080/proxy

const http = require('http');
const https = require('https');
const url = require('url');

const PORT = 8080;

const server = http.createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 只处理 /proxy 路径
  if (!req.url.startsWith('/proxy')) {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found. Use /proxy?url=<target_url>' }));
    return;
  }

  try {
    const parsedUrl = url.parse(req.url, true);
    const targetUrl = parsedUrl.query.url;

    if (!targetUrl) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Missing target URL. Use /proxy?url=<target_url>' }));
      return;
    }

    console.log(`Proxying ${req.method} request to: ${targetUrl}`);

    const targetParsed = url.parse(targetUrl);
    const isHttps = targetParsed.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    // 收集请求体数据
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      const options = {
        hostname: targetParsed.hostname,
        port: targetParsed.port || (isHttps ? 443 : 80),
        path: targetParsed.path,
        method: req.method,
        headers: {
          ...req.headers,
          host: targetParsed.host,
        }
      };

      // 移除可能导致问题的头
      delete options.headers['origin'];
      delete options.headers['referer'];

      const proxyReq = httpModule.request(options, (proxyRes) => {
        // 复制响应头
        Object.keys(proxyRes.headers).forEach(key => {
          res.setHeader(key, proxyRes.headers[key]);
        });

        // 确保CORS头存在
        res.setHeader('Access-Control-Allow-Origin', '*');
        
        res.writeHead(proxyRes.statusCode);
        proxyRes.pipe(res);
      });

      proxyReq.on('error', (err) => {
        console.error('Proxy request error:', err);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ 
          error: 'Proxy request failed', 
          details: err.message 
        }));
      });

      if (body) {
        proxyReq.write(body);
      }
      proxyReq.end();
    });

  } catch (err) {
    console.error('Request processing error:', err);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ 
      error: 'Request processing failed', 
      details: err.message 
    }));
  }
});

server.listen(PORT, () => {
  console.log(`CORS Proxy Server running on http://localhost:${PORT}`);
  console.log('Usage:');
  console.log('  1. Start this server: node cors-proxy-server.js');
  console.log('  2. In AI Player settings, set Base URL to: http://localhost:8080/proxy?url=<your_api_url>');
  console.log('  3. Example for Alibaba Cloud: http://localhost:8080/proxy?url=https://dashscope.aliyuncs.com/compatible-mode/v1');
  console.log('');
  console.log('Note: This is for development only. In production, use the Electron version.');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\nShutting down CORS proxy server...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\nShutting down CORS proxy server...');
  server.close(() => {
    console.log('Server closed.');
    process.exit(0);
  });
});
