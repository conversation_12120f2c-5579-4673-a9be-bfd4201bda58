/**
 * MCP (Model Context Protocol) 客户端
 * 使用官方 @modelcontextprotocol/sdk 实现
 */
import { MCPConnection, MCPServerConfig } from './types';

type EventHandler = (data: any) => void;

export declare class MCPClient {
    private connections;
    private messageHandlers;

    /**
     * 连接到MCP服务器
     */
    connect(config: MCPServerConfig): Promise<MCPConnection>;

    /**
     * 断开MCP服务器连接
     */
    disconnect(id: string): void;

    /**
     * 初始化连接，获取服务器能力
     */
    private initializeConnection;

    /**
     * 调用MCP工具
     */
    callTool(id: string, toolName: string, args?: any): Promise<any>;

    /**
     * 获取MCP资源
     */
    getResource(id: string, uri: string): Promise<any>;

    /**
     * 获取连接状态
     */
    getConnection(id: string): MCPConnection | null;

    /**
     * 获取所有连接
     */
    getAllConnections(): MCPConnection[];

    /**
     * 处理连接错误
     */
    private handleConnectionError;

    /**
     * 处理连接关闭
     */
    private handleConnectionClosed;

    /**
     * 事件发射器
     */
    private emit;

    /**
     * 添加事件监听器
     */
    on(event: string, handler: EventHandler): void;

    /**
     * 移除事件监听器
     */
    off(event: string, handler: EventHandler): void;
}

export {};
