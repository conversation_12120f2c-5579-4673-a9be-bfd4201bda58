import React from 'react';
import { createRoot } from 'react-dom/client';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import { setupMockElectronAPI } from './utils/mockElectronAPI';
import './styles/global.css';

// 设置 Mock Electron API（如果在浏览器环境中）
setupMockElectronAPI();

// 创建深色主题
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#007acc',
    },
    secondary: {
      main: '#404040',
    },
    background: {
      default: '#1a1a1a',
      paper: '#2d2d2d',
    },
    text: {
      primary: '#ffffff',
      secondary: '#cccccc',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      'sans-serif',
    ].join(','),
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
        },
      },
    },
  },
});

// 获取根元素并创建 React 根
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = createRoot(container);

// 渲染应用
root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <ThemeProvider theme={darkTheme}>
        <CssBaseline />
        <App />
      </ThemeProvider>
    </ErrorBoundary>
  </React.StrictMode>
);

// 在开发模式下启用热重载
if (import.meta.hot) {
  import.meta.hot.accept('./App', (newModule) => {
    if (newModule) {
      const NextApp = newModule.default;
      root.render(
        <React.StrictMode>
          <ErrorBoundary>
            <ThemeProvider theme={darkTheme}>
              <CssBaseline />
              <NextApp />
            </ThemeProvider>
          </ErrorBoundary>
        </React.StrictMode>
      );
    }
  });
}
