import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  TextField,
  FormControlLabel,
  Checkbox,
  Slider,
  Typography,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { useAppStore } from '../stores/appStore';
import { Configuration } from '@shared/types';
import MCPSettings from './MCPSettings';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const SettingsModal: React.FC = () => {
  const [localSettings, setLocalSettings] = useState<Partial<Configuration>>({});
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; error?: string } | null>(null);
  
  const {
    isSettingsModalOpen,
    settings,
    closeSettingsModal,
    saveSettings,
    settingsDefaultTab
  } = useAppStore();

  // 使用settingsDefaultTab作为初始值，确保是有效数字
  const [tabValue, setTabValue] = useState(() => {
    const validTab = typeof settingsDefaultTab === 'number' && !isNaN(settingsDefaultTab) && settingsDefaultTab >= 0 && settingsDefaultTab <= 2
      ? settingsDefaultTab
      : 0;
    return validTab;
  });

  useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    }
  }, [settings]);

  // 当settingsDefaultTab改变时更新tabValue
  useEffect(() => {
    // 确保settingsDefaultTab是一个有效的数字
    const validTab = typeof settingsDefaultTab === 'number' && !isNaN(settingsDefaultTab) && settingsDefaultTab >= 0 && settingsDefaultTab <= 2
      ? settingsDefaultTab
      : 0;

    setTabValue(validTab);
  }, [settingsDefaultTab]);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSave = async () => {
    try {
      // 保存设置到应用状态
      await saveSettings(localSettings);

      // 同时更新OpenAI配置
      if (typeof window !== 'undefined' && window.electronAPI) {
        const aiConfig = {
          apiKey: getSetting('ai.apiKey', ''),
          baseURL: getSetting('ai.apiUrl', 'https://api.openai.com/v1'),
          model: getSetting('ai.model', 'gpt-3.5-turbo'),
          temperature: getSetting('ai.temperature', 0.7),
          maxTokens: getSetting('ai.maxTokens', 2000),
          systemPrompt: getSetting('ai.systemPrompt', '你是一个有用的AI助手。')
        };

        await window.electronAPI.updateOpenaiConfig(aiConfig);
        console.log('AI配置已更新:', aiConfig);
      }

      closeSettingsModal();
    } catch (error) {
      console.error('保存设置失败:', error);
      // 可以在这里添加错误提示
    }
  };

  const handleCancel = () => {
    closeSettingsModal();
  };

  const updateSetting = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...localSettings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    setLocalSettings(newSettings);
  };

  const getSetting = (path: string, defaultValue: any = '') => {
    const keys = path.split('.');
    let current: any = localSettings;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }

    return current;
  };

  const handleTestConnection = async () => {
    if (typeof window === 'undefined' || !window.electronAPI) {
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      // 先保存当前AI配置
      const aiConfig = {
        apiKey: getSetting('ai.apiKey', ''),
        baseURL: getSetting('ai.apiUrl', 'https://api.openai.com/v1'),
        model: getSetting('ai.model', 'gpt-3.5-turbo'),
        temperature: getSetting('ai.temperature', 0.7),
        maxTokens: getSetting('ai.maxTokens', 2000),
        systemPrompt: getSetting('ai.systemPrompt', '你是一个有用的AI助手。')
      };

      await window.electronAPI.updateOpenaiConfig(aiConfig);

      // 然后测试连接
      const result = await window.electronAPI.testOpenaiConnection();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Test failed'
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <Dialog
      open={isSettingsModalOpen}
      onClose={handleCancel}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        设置
        <IconButton onClick={handleCancel}>
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="播放器" />
            <Tab label="AI聊天" />
            <Tab label="网络" />
            <Tab label="MCP服务器" />
          </Tabs>
        </Box>

        {/* 播放器设置 */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" sx={{ mb: 2 }}>播放器设置</Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={getSetting('player.autoplay', false)}
                onChange={(e) => updateSetting('player.autoplay', e.target.checked)}
              />
            }
            label="自动播放"
            sx={{ mb: 2 }}
          />

          <Typography gutterBottom>音量</Typography>
          <Slider
            value={getSetting('player.volume', 50)}
            onChange={(_, value) => updateSetting('player.volume', value)}
            min={0}
            max={100}
            valueLabelDisplay="auto"
            sx={{ mb: 3 }}
          />

          <Typography gutterBottom>播放速度</Typography>
          <Slider
            value={getSetting('player.playbackRate', 1.0)}
            onChange={(_, value) => updateSetting('player.playbackRate', value)}
            min={0.25}
            max={2.0}
            step={0.25}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />
        </TabPanel>

        {/* AI聊天设置 */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" sx={{ mb: 2 }}>AI聊天设置</Typography>

          <TextField
            fullWidth
            label="API密钥"
            type="password"
            value={getSetting('ai.apiKey', '')}
            onChange={(e) => {
              updateSetting('ai.apiKey', e.target.value);
              setTestResult(null); // 清除之前的测试结果
            }}
            required
            helperText="请输入您的OpenAI API密钥"
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="Base URL"
            value={getSetting('ai.apiUrl', 'https://api.openai.com/v1')}
            onChange={(e) => {
              updateSetting('ai.apiUrl', e.target.value);
              setTestResult(null);
            }}
            helperText="OpenAI API的基础URL，通常不需要修改"
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="模型"
            value={getSetting('ai.model', 'gpt-3.5-turbo')}
            onChange={(e) => {
              updateSetting('ai.model', e.target.value);
              setTestResult(null);
            }}
            helperText="请输入AI模型名称，例如：gpt-3.5-turbo, gpt-4, gpt-4o"
            sx={{ mb: 2 }}
          />

          <Box sx={{ mb: 2 }}>
            <Typography gutterBottom>
              温度 (Temperature): {getSetting('ai.temperature', 0.7)}
            </Typography>
            <Slider
              value={getSetting('ai.temperature', 0.7)}
              onChange={(_, value) => {
                updateSetting('ai.temperature', value);
                setTestResult(null);
              }}
              min={0}
              max={2}
              step={0.1}
              marks={[
                { value: 0, label: '0 (确定性)' },
                { value: 1, label: '1 (平衡)' },
                { value: 2, label: '2 (创造性)' }
              ]}
            />
          </Box>

          <TextField
            fullWidth
            label="最大令牌数"
            type="number"
            value={getSetting('ai.maxTokens', 2000)}
            onChange={(e) => {
              updateSetting('ai.maxTokens', parseInt(e.target.value) || 2000);
              setTestResult(null);
            }}
            inputProps={{ min: 1, max: 4000 }}
            helperText="生成回复的最大长度"
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="系统提示"
            value={getSetting('ai.systemPrompt', '你是一个有用的AI助手。')}
            onChange={(e) => {
              updateSetting('ai.systemPrompt', e.target.value);
              setTestResult(null);
            }}
            multiline
            rows={3}
            helperText="定义AI助手的行为和角色"
            sx={{ mb: 2 }}
          />

          {/* 测试连接按钮 */}
          <Box sx={{ mb: 2 }}>
            <Button
              onClick={handleTestConnection}
              disabled={!getSetting('ai.apiKey', '') || testing}
              startIcon={testing ? <CircularProgress size={16} /> : null}
              variant="outlined"
            >
              {testing ? '测试中...' : '测试连接'}
            </Button>
          </Box>

          {/* 测试结果 */}
          {testResult && (
            <Alert severity={testResult.success ? 'success' : 'error'} sx={{ mb: 2 }}>
              {testResult.success
                ? 'OpenAI API连接测试成功！'
                : `连接测试失败: ${testResult.error}`
              }
            </Alert>
          )}

          {/* CORS帮助提示 */}
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>浏览器环境使用提示：</strong>
            </Typography>
            <Typography variant="body2" component="div">
              • 推荐使用Electron版本以获得最佳体验<br/>
              • 如遇到CORS错误，可使用代理服务器：<br/>
              &nbsp;&nbsp;1. 运行：<code>node cors-proxy-server.js</code><br/>
              &nbsp;&nbsp;2. Base URL改为：<code>http://localhost:8080/proxy?url=你的API地址</code><br/>
              • OpenAI官方API支持直接访问，无需代理
            </Typography>
          </Alert>
        </TabPanel>

        {/* 网络设置 */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" sx={{ mb: 2 }}>网络设置</Typography>
          
          <FormControlLabel
            control={
              <Checkbox
                checked={getSetting('network.proxy.enabled', false)}
                onChange={(e) => updateSetting('network.proxy.enabled', e.target.checked)}
              />
            }
            label="启用代理"
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="代理地址"
            value={getSetting('network.proxy.host', '')}
            onChange={(e) => updateSetting('network.proxy.host', e.target.value)}
            disabled={!getSetting('network.proxy.enabled', false)}
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="代理端口"
            type="number"
            value={getSetting('network.proxy.port', 8080)}
            onChange={(e) => updateSetting('network.proxy.port', parseInt(e.target.value))}
            disabled={!getSetting('network.proxy.enabled', false)}
            sx={{ mb: 2 }}
          />

          <TextField
            fullWidth
            label="User Agent"
            value={getSetting('network.userAgent', '')}
            onChange={(e) => updateSetting('network.userAgent', e.target.value)}
            sx={{ mb: 2 }}
          />
        </TabPanel>

        {/* MCP服务器设置 */}
        <TabPanel value={tabValue} index={3}>
          <MCPSettings />
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleCancel}>取消</Button>
        <Button onClick={handleSave} variant="contained">保存</Button>
      </DialogActions>
    </Dialog>
  );
};

export default SettingsModal;
