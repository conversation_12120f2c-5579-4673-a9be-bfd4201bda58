import React, { useRef, useEffect, useState } from 'react';
import { Box, Paper, Typography } from '@mui/material';
import { useAppStore } from '../stores/appStore';

const VideoPlayer: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { currentVideo, updateVideoInfo } = useAppStore();
  const [isHovering, setIsHovering] = useState(false);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadStart = () => {
      updateVideoInfo({ paused: true });
    };

    const handleCanPlay = () => {
      updateVideoInfo({ 
        duration: video.duration,
        paused: video.paused 
      });
    };

    const handlePlay = () => {
      updateVideoInfo({ paused: false, ended: false });
    };

    const handlePause = () => {
      updateVideoInfo({ paused: true });
    };

    const handleEnded = () => {
      updateVideoInfo({ paused: true, ended: true });
    };

    const handleTimeUpdate = () => {
      updateVideoInfo({ 
        currentTime: video.currentTime,
        duration: video.duration 
      });
    };

    const handleVolumeChange = () => {
      updateVideoInfo({ 
        volume: Math.round(video.volume * 100),
        muted: video.muted 
      });
    };

    const handleError = (e: Event) => {
      const target = e.target as HTMLVideoElement;
      const error = target.error;
      console.error('视频播放错误:', {
        code: error?.code,
        message: error?.message,
        src: target.src,
        networkState: target.networkState,
        readyState: target.readyState
      });

      // 提供更详细的错误信息
      let errorMessage = '视频加载失败';
      if (error) {
        switch (error.code) {
          case MediaError.MEDIA_ERR_ABORTED:
            errorMessage = '视频加载被中止';
            break;
          case MediaError.MEDIA_ERR_NETWORK:
            errorMessage = '网络错误，无法加载视频';
            break;
          case MediaError.MEDIA_ERR_DECODE:
            errorMessage = '视频解码错误';
            break;
          case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
            errorMessage = '不支持的视频格式或来源';
            break;
          default:
            errorMessage = `视频错误 (代码: ${error.code})`;
        }
      }

      console.warn(errorMessage);
      updateVideoInfo({ paused: true });
    };

    // 添加事件监听器
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('ended', handleEnded);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('error', handleError);

    // 清理函数
    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('ended', handleEnded);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('error', handleError);
    };
  }, [updateVideoInfo]);

  // 当视频源改变时更新视频元素
  useEffect(() => {
    const video = videoRef.current;
    if (video && currentVideo?.src) {
      console.log('Loading video:', currentVideo.src);

      // 清除之前的源
      video.src = '';
      video.load();

      // 处理不同类型的视频源
      let videoSrc = currentVideo.src;

      // 如果是file://开头的blob URL，需要去掉file://前缀
      if (videoSrc.startsWith('file://blob:')) {
        videoSrc = videoSrc.replace('file://', '');
        console.log('Fixed blob URL:', videoSrc);
      }

      // 设置新的源
      video.src = videoSrc;

      // 尝试加载视频
      video.load();

      // 添加一些调试信息
      video.addEventListener('loadstart', () => {
        console.log('Video load started');
      }, { once: true });

      video.addEventListener('loadedmetadata', () => {
        console.log('Video metadata loaded:', {
          duration: video.duration,
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight
        });
      }, { once: true });

      video.addEventListener('canplay', () => {
        console.log('Video can start playing');
      }, { once: true });

      video.addEventListener('error', (e) => {
        console.error('Video loading error in useEffect:', {
          src: video.src,
          error: video.error
        });
      }, { once: true });
    }
  }, [currentVideo?.src]);

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '00:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusText = (): string => {
    if (!currentVideo) return '就绪';
    if (currentVideo.ended) return '播放完成';
    if (currentVideo.paused) return '暂停';
    return '播放中';
  };

  return (
    <Box
      sx={{
        flex: 1,
        position: 'relative',
        bgcolor: '#000000',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: 0,
      }}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {currentVideo ? (
        <>
          <video
            ref={videoRef}
            controls
            preload="metadata"
            crossOrigin="anonymous"
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'contain',
            }}
          >
            <source src={currentVideo.src} type="video/mp4" />
            <source src={currentVideo.src} type="video/webm" />
            <source src={currentVideo.src} type="video/ogg" />
            您的浏览器不支持视频播放。
          </video>

          {/* 视频信息覆盖层 - 鼠标悬停时隐藏 */}
          {!isHovering && (
            <Paper
              sx={{
                position: 'absolute',
                top: 16,
                left: 16,
                p: 1.5,
                bgcolor: 'rgba(0, 0, 0, 0.7)',
                backdropFilter: 'blur(4px)',
                pointerEvents: 'none',
                transition: 'opacity 0.3s ease-in-out',
              }}
            >
            <Typography variant="h6" sx={{ mb: 0.5, color: 'white' }}>
              {currentVideo.title}
            </Typography>
            <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
              {getStatusText()}
              {currentVideo.duration > 0 && (
                <> • {formatTime(currentVideo.currentTime)} / {formatTime(currentVideo.duration)}</>
              )}
            </Typography>
          </Paper>
          )}
        </>
      ) : (
        <Box
          sx={{
            textAlign: 'center',
            color: 'text.secondary',
            p: 4,
          }}
        >
          <Typography variant="h5" sx={{ mb: 2 }}>
            AI Player
          </Typography>
          <Typography variant="body1">
            请选择视频文件或输入网络链接开始播放
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default VideoPlayer;
