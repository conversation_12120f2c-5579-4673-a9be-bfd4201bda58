{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "preserve", "declaration": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/main/**/*", "src/shared/**/*"], "exclude": ["src/renderer/**/*", "node_modules", "dist"]}