import React, { useEffect } from 'react';
import { Box } from '@mui/material';
import { useAppStore } from './stores/appStore';
import Toolbar from './components/Toolbar';
import VideoPlayer from './components/VideoPlayer';
import ChatPanel from './components/ChatPanel';
import StatusBar from './components/StatusBar';
import SettingsModal from './components/SettingsModal';
import { setupMockElectronAPI } from './utils/mockElectronAPI';

const App: React.FC = () => {
  const { 
    isReady, 
    isChatPanelOpen, 
    isSettingsModalOpen,
    initializeApp,
    setupElectronListeners 
  } = useAppStore();

  useEffect(() => {
    // 检查是否在 Electron 环境中
    if (typeof window !== 'undefined' && window.electronAPI) {
      // 初始化应用
      initializeApp();

      // 设置 Electron 事件监听器
      setupElectronListeners();

      // 清理函数
      return () => {
        // 移除所有监听器
        if (window.electronAPI) {
          window.electronAPI.removeAllListeners('open-video-file');
          window.electronAPI.removeAllListeners('toggle-play');
          window.electronAPI.removeAllListeners('stop-video');
          window.electronAPI.removeAllListeners('toggle-chat-panel');
          window.electronAPI.removeAllListeners('mcp-message');
        }
      };
    } else {
      console.log('Running in browser environment');
      // 设置 Mock Electron API 和数据库
      setupMockElectronAPI();

      // 初始化应用
      initializeApp();
    }
  }, [initializeApp, setupElectronListeners]);

  if (!isReady) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
        bgcolor="background.default"
        color="text.primary"
      >
        正在加载 AI Player...
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100vh',
        bgcolor: 'background.default',
        color: 'text.primary',
        overflow: 'hidden',
      }}
    >
      {/* 顶部工具栏 */}
      <Toolbar />

      {/* 主内容区域 */}
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          overflow: 'hidden',
        }}
      >
        {/* 视频播放区域 */}
        <VideoPlayer />

        {/* AI聊天面板 */}
        {isChatPanelOpen && <ChatPanel />}
      </Box>

      {/* 底部状态栏 */}
      <StatusBar />

      {/* 设置模态框 */}
      {isSettingsModalOpen && <SettingsModal />}
    </Box>
  );
};

export default App;
