/// <reference types="vite/client" />

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string;
  readonly DEV: boolean;
  readonly PROD: boolean;
  readonly MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// 全局变量类型定义
declare const __DEV__: boolean;

// Electron API 类型定义
interface ElectronAPI {
  // 应用信息
  getAppVersion: () => Promise<string>;

  // 消息框
  showMessageBox: (options: Electron.MessageBoxOptions) => Promise<Electron.MessageBoxReturnValue | null>;

  // 视频播放相关
  onOpenVideoFile: (callback: (event: Electron.IpcRendererEvent, filePath: string) => void) => void;
  onTogglePlay: (callback: (event: Electron.IpcRendererEvent) => void) => void;
  onStopVideo: (callback: (event: Electron.IpcRendererEvent) => void) => void;

  // AI聊天相关
  onToggleChatPanel: (callback: (event: Electron.IpcRendererEvent) => void) => void;

  // 移除监听器
  removeAllListeners: (channel: string) => void;

  // MCP 客户端相关
  mcpConnect: (serverConfig: any) => Promise<any>;
  mcpDisconnect: (serverId: string) => Promise<void>;
  mcpSendMessage: (serverId: string, message: any) => Promise<any>;
  onMcpMessage: (callback: (event: Electron.IpcRendererEvent, data: any) => void) => void;

  // 网络视频播放相关
  validateVideoUrl: (url: string) => Promise<boolean>;

  // 文件系统相关
  selectVideoFile: () => Promise<string | null>;

  // 设置相关
  getSettings: () => Promise<any>;
  saveSettings: (settings: any) => Promise<void>;

  // 窗口控制
  minimizeWindow: () => Promise<void>;
  maximizeWindow: () => Promise<void>;
  closeWindow: () => Promise<void>;

  // AI 聊天相关
  aiChat: (messages: Array<{role: string; content: string}>, sessionId?: string) => Promise<{success: boolean; data?: any; error?: string}>;

  // 聊天会话管理
  createChatSession: (title: string) => Promise<{success: boolean; data?: string; error?: string}>;
  getChatSessions: () => Promise<{success: boolean; data?: any[]; error?: string}>;
  getChatMessages: (sessionId: string, limit?: number) => Promise<{success: boolean; data?: any[]; error?: string}>;
  deleteChatSession: (sessionId: string) => Promise<{success: boolean; error?: string}>;

  // 文件搜索相关
  indexFiles: (paths: string[]) => Promise<{success: boolean; data?: any; error?: string}>;
  searchFiles: (query: string, options?: any) => Promise<{success: boolean; data?: any; error?: string}>;

  // OpenAI 配置
  getOpenaiConfig: () => Promise<any>;
  updateOpenaiConfig: (config: any) => Promise<{success: boolean; error?: string}>;
  testOpenaiConnection: () => Promise<{success: boolean; error?: string}>;

  // MCP 服务器管理
  getMcpServers: () => Promise<{success: boolean; data?: any[]; error?: string}>;
  addRemoteMcpServer: (config: any) => Promise<{success: boolean; error?: string}>;
  removeMcpServer: (serverId: string) => Promise<{success: boolean; error?: string}>;
  getAvailableTools: () => Promise<{success: boolean; data?: any[]; error?: string}>;
}

// 全局 Window 接口扩展
declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
