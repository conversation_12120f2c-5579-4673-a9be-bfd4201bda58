import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { useAppStore } from '../stores/appStore';

const StatusBar: React.FC = () => {
  const { appVersion, mcpConnections } = useAppStore();

  const connectedMCPCount = mcpConnections.filter(
    conn => conn.status === 'connected'
  ).length;

  const getConnectionStatus = () => {
    if (connectedMCPCount > 0) {
      return `MCP: ${connectedMCPCount} 个连接`;
    }
    return '离线';
  };

  const getConnectionColor = () => {
    return connectedMCPCount > 0 ? 'success' : 'default';
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        px: 2,
        py: 0.5,
        bgcolor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider',
        minHeight: 32,
      }}
    >
      {/* 左侧信息 */}
      <Typography variant="caption" color="text.secondary">
        AI Player v{appVersion}
      </Typography>

      {/* 右侧状态 */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Chip
          label={getConnectionStatus()}
          color={getConnectionColor()}
          size="small"
          variant="outlined"
        />
      </Box>
    </Box>
  );
};

export default StatusBar;
