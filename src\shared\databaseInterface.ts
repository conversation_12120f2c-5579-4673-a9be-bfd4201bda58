// 统一的数据库接口，支持Electron和浏览器环境
import { 
  ChatMessage, 
  AppSettings, 
  FileSearchIndex, 
  ChatSession 
} from './database';

// 数据库接口定义
export interface IDatabaseManager {
  initialize(): Promise<void>;
  
  // 设置管理
  getSetting(key: string): Promise<string | null>;
  setSetting(key: string, value: string, category?: string): Promise<void>;
  getSettingsByCategory(category: string): Promise<AppSettings[]>;
  
  // 聊天会话管理
  createChatSession(title: string): Promise<string>;
  getChatSessions(): Promise<ChatSession[]>;
  updateChatSession(id: string, title?: string): Promise<void>;
  deleteChatSession(id: string): Promise<void>;
  
  // 聊天消息管理
  addChatMessage(message: Omit<ChatMessage, 'timestamp'>): Promise<void>;
  getChatMessages(sessionId?: string, limit?: number): Promise<ChatMessage[]>;
  clearChatMessages(sessionId?: string): Promise<void>;
  
  // 文件搜索索引管理（可选，浏览器环境可能不支持）
  addFileToIndex?(file: Omit<FileSearchIndex, 'id' | 'indexedAt'>): Promise<void>;
  searchFiles?(query: string, limit?: number): Promise<FileSearchIndex[]>;
  removeFileFromIndex?(filePath: string): Promise<void>;
  
  // 数据库维护
  vacuum?(): Promise<void>;
  close(): Promise<void>;
}

// 数据库工厂函数
export async function createDatabaseManager(): Promise<IDatabaseManager> {
  if (typeof window !== 'undefined' && typeof window.electronAPI === 'undefined') {
    // 浏览器环境
    console.log('Using browser database (localStorage)');
    const { browserDatabaseManager } = await import('../renderer/utils/browserDatabase');
    return browserDatabaseManager;
  } else {
    // Electron环境
    console.log('Using Electron database (SQLite)');
    const { databaseManager } = await import('./database');
    return databaseManager;
  }
}

// 全局数据库实例
let globalDatabaseManager: IDatabaseManager | null = null;

export async function getDatabaseManager(): Promise<IDatabaseManager> {
  if (!globalDatabaseManager) {
    globalDatabaseManager = await createDatabaseManager();
  }
  return globalDatabaseManager;
}

// 数据库初始化函数
export async function initializeDatabase(): Promise<IDatabaseManager> {
  const dbManager = await getDatabaseManager();
  await dbManager.initialize();
  return dbManager;
}

// 配置相关的辅助函数
export class DatabaseConfigManager {
  private dbManager: IDatabaseManager;

  constructor(dbManager: IDatabaseManager) {
    this.dbManager = dbManager;
  }

  // 保存完整的应用配置
  async saveAppConfig(config: any): Promise<void> {
    const configString = JSON.stringify(config);
    await this.dbManager.setSetting('app_config', configString, 'application');
  }

  // 加载完整的应用配置
  async loadAppConfig(): Promise<any | null> {
    const configString = await this.dbManager.getSetting('app_config');
    if (configString) {
      try {
        return JSON.parse(configString);
      } catch (error) {
        console.error('Failed to parse app config:', error);
        return null;
      }
    }
    return null;
  }

  // 保存AI配置
  async saveAIConfig(config: any): Promise<void> {
    const configString = JSON.stringify(config);
    await this.dbManager.setSetting('ai_config', configString, 'ai');
  }

  // 加载AI配置
  async loadAIConfig(): Promise<any | null> {
    const configString = await this.dbManager.getSetting('ai_config');
    if (configString) {
      try {
        return JSON.parse(configString);
      } catch (error) {
        console.error('Failed to parse AI config:', error);
        return null;
      }
    }
    return null;
  }

  // 保存网络配置
  async saveNetworkConfig(config: any): Promise<void> {
    const configString = JSON.stringify(config);
    await this.dbManager.setSetting('network_config', configString, 'network');
  }

  // 加载网络配置
  async loadNetworkConfig(): Promise<any | null> {
    const configString = await this.dbManager.getSetting('network_config');
    if (configString) {
      try {
        return JSON.parse(configString);
      } catch (error) {
        console.error('Failed to parse network config:', error);
        return null;
      }
    }
    return null;
  }

  // 保存播放器配置
  async savePlayerConfig(config: any): Promise<void> {
    const configString = JSON.stringify(config);
    await this.dbManager.setSetting('player_config', configString, 'player');
  }

  // 加载播放器配置
  async loadPlayerConfig(): Promise<any | null> {
    const configString = await this.dbManager.getSetting('player_config');
    if (configString) {
      try {
        return JSON.parse(configString);
      } catch (error) {
        console.error('Failed to parse player config:', error);
        return null;
      }
    }
    return null;
  }

  // 获取所有配置
  async getAllConfigs(): Promise<{
    app?: any;
    ai?: any;
    network?: any;
    player?: any;
  }> {
    const [app, ai, network, player] = await Promise.all([
      this.loadAppConfig(),
      this.loadAIConfig(),
      this.loadNetworkConfig(),
      this.loadPlayerConfig()
    ]);

    return { app, ai, network, player };
  }

  // 清除所有配置
  async clearAllConfigs(): Promise<void> {
    const settings = await this.dbManager.getSettingsByCategory('application');
    const aiSettings = await this.dbManager.getSettingsByCategory('ai');
    const networkSettings = await this.dbManager.getSettingsByCategory('network');
    const playerSettings = await this.dbManager.getSettingsByCategory('player');

    // 注意：这里需要实现删除设置的方法，当前接口中没有
    // 可以通过设置空值来实现"删除"
    await this.dbManager.setSetting('app_config', '', 'application');
    await this.dbManager.setSetting('ai_config', '', 'ai');
    await this.dbManager.setSetting('network_config', '', 'network');
    await this.dbManager.setSetting('player_config', '', 'player');
  }
}

// 聊天相关的辅助函数
export class DatabaseChatManager {
  private dbManager: IDatabaseManager;

  constructor(dbManager: IDatabaseManager) {
    this.dbManager = dbManager;
  }

  // 创建新的聊天会话并返回会话信息
  async createNewChatSession(title?: string): Promise<ChatSession> {
    const sessionTitle = title || `聊天会话 ${new Date().toLocaleString()}`;
    const sessionId = await this.dbManager.createChatSession(sessionTitle);
    
    return {
      id: sessionId,
      title: sessionTitle,
      createdAt: new Date(),
      updatedAt: new Date(),
      messageCount: 0
    };
  }

  // 添加用户消息和AI回复
  async addConversation(sessionId: string, userMessage: string, aiResponse: string): Promise<void> {
    // 添加用户消息
    await this.dbManager.addChatMessage({
      id: `msg_${Date.now()}_user_${Math.random().toString(36).substring(2, 11)}`,
      role: 'user',
      content: userMessage,
      sessionId
    });

    // 添加AI回复
    await this.dbManager.addChatMessage({
      id: `msg_${Date.now()}_ai_${Math.random().toString(36).substring(2, 11)}`,
      role: 'assistant',
      content: aiResponse,
      sessionId
    });
  }

  // 获取会话的最近消息
  async getRecentMessages(sessionId: string, limit: number = 20): Promise<ChatMessage[]> {
    const messages = await this.dbManager.getChatMessages(sessionId, limit);
    // 按时间正序排列（最早的在前面）
    return messages.reverse();
  }

  // 搜索消息内容
  async searchMessages(query: string, sessionId?: string): Promise<ChatMessage[]> {
    const messages = await this.dbManager.getChatMessages(sessionId);
    return messages.filter(message => 
      message.content.toLowerCase().includes(query.toLowerCase())
    );
  }

  // 导出会话为文本
  async exportSession(sessionId: string): Promise<string> {
    const session = (await this.dbManager.getChatSessions()).find(s => s.id === sessionId);
    const messages = await this.getRecentMessages(sessionId, 1000);

    if (!session) {
      throw new Error('Session not found');
    }

    let exportText = `# ${session.title}\n`;
    exportText += `创建时间: ${session.createdAt.toLocaleString()}\n`;
    exportText += `更新时间: ${session.updatedAt.toLocaleString()}\n`;
    exportText += `消息数量: ${session.messageCount}\n\n`;

    messages.forEach(message => {
      const role = message.role === 'user' ? '用户' : 'AI助手';
      const timestamp = new Date(message.timestamp).toLocaleString();
      exportText += `## ${role} (${timestamp})\n${message.content}\n\n`;
    });

    return exportText;
  }
}

// 创建配置管理器
export function createConfigManager(dbManager: IDatabaseManager): DatabaseConfigManager {
  return new DatabaseConfigManager(dbManager);
}

// 创建聊天管理器
export function createChatManager(dbManager: IDatabaseManager): DatabaseChatManager {
  return new DatabaseChatManager(dbManager);
}
