import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as TestIcon,
  CheckCircle as ConnectedIcon,
  Error as ErrorIcon,
  Warning as DisconnectedIcon
} from '@mui/icons-material';
import {
  MCPServerInfo,
  MCPToolInfo,
  MCPServerConfig,
  MCPServerTestResult
} from '../../shared/types';

const MCPSettings: React.FC = () => {
  const [servers, setServers] = useState<MCPServerInfo[]>([]);
  const [tools, setTools] = useState<MCPToolInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [addDialogO<PERSON>, setAddDialogOpen] = useState(false);
  const [testingServer, setTestingServer] = useState<string | null>(null);
  const [newServerConfig, setNewServerConfig] = useState<Partial<MCPServerConfig>>({
    name: '',
    url: '',
    enabled: true,
    autoConnect: false,
    auth: { type: 'none' }
  });

  useEffect(() => {
    loadServers();
    loadTools();
  }, []);

  const loadServers = async () => {
    try {
      const response = await window.electronAPI.getMcpServers();
      if (response.success) {
        setServers(response.data);
      }
    } catch (error) {
      console.error('Failed to load MCP servers:', error);
    }
  };

  const loadTools = async () => {
    try {
      const response = await window.electronAPI.getMcpTools();
      if (response.success) {
        setTools(response.data);
      }
    } catch (error) {
      console.error('Failed to load MCP tools:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddServer = async () => {
    if (!newServerConfig.name || !newServerConfig.url) {
      return;
    }

    try {
      const config: MCPServerConfig = {
        id: `server_${Date.now()}`,
        name: newServerConfig.name,
        url: newServerConfig.url,
        enabled: newServerConfig.enabled || true,
        autoConnect: newServerConfig.autoConnect || false,
        auth: newServerConfig.auth || { type: 'none' },
        description: newServerConfig.description
      };

      const response = await window.electronAPI.addRemoteMcpServer(config);
      if (response.success) {
        setAddDialogOpen(false);
        setNewServerConfig({
          name: '',
          url: '',
          enabled: true,
          autoConnect: false,
          auth: { type: 'none' }
        });
        await loadServers();
        await loadTools();
      }
    } catch (error) {
      console.error('Failed to add MCP server:', error);
    }
  };

  const handleRemoveServer = async (serverId: string) => {
    try {
      const response = await window.electronAPI.removeMcpServer(serverId);
      if (response.success) {
        await loadServers();
        await loadTools();
      }
    } catch (error) {
      console.error('Failed to remove MCP server:', error);
    }
  };

  const handleTestServer = async (serverId: string) => {
    setTestingServer(serverId);
    try {
      const response: MCPServerTestResult = await window.electronAPI.testMcpServer(serverId);
      if (response.success) {
        console.log('Test result:', response);
        // 可以显示测试结果
      }
    } catch (error) {
      console.error('Failed to test MCP server:', error);
    } finally {
      setTestingServer(null);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <ConnectedIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <DisconnectedIcon color="warning" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'error':
        return 'error';
      default:
        return 'warning';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h6">MCP 服务器管理</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setAddDialogOpen(true)}
        >
          添加服务器
        </Button>
      </Box>

      <Grid container spacing={3}>
        {/* 服务器列表 */}
        <Grid item xs={12} md={8}>
          <Typography variant="subtitle1" gutterBottom>
            服务器列表
          </Typography>
          {servers.length === 0 ? (
            <Alert severity="info">
              暂无MCP服务器。点击"添加服务器"按钮来添加第一个服务器。
            </Alert>
          ) : (
            <Grid container spacing={2}>
              {servers.map((server) => (
                <Grid item xs={12} key={server.id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" justifyContent="space-between">
                        <Box display="flex" alignItems="center" gap={1}>
                          {getStatusIcon(server.status)}
                          <Typography variant="h6">{server.name}</Typography>
                          <Chip
                            label={server.type === 'local' ? '本地' : '远程'}
                            size="small"
                            color={server.type === 'local' ? 'primary' : 'secondary'}
                          />
                          <Chip
                            label={server.status}
                            size="small"
                            color={getStatusColor(server.status) as any}
                          />
                        </Box>
                        <Box>
                          {server.type === 'remote' && (
                            <>
                              <IconButton
                                onClick={() => handleTestServer(server.id)}
                                disabled={testingServer === server.id}
                                title="测试连接"
                              >
                                {testingServer === server.id ? (
                                  <CircularProgress size={20} />
                                ) : (
                                  <TestIcon />
                                )}
                              </IconButton>
                              <IconButton
                                onClick={() => handleRemoveServer(server.id)}
                                color="error"
                                title="删除服务器"
                              >
                                <DeleteIcon />
                              </IconButton>
                            </>
                          )}
                        </Box>
                      </Box>
                      {server.url && (
                        <Typography variant="body2" color="text.secondary" mt={1}>
                          URL: {server.url}
                        </Typography>
                      )}
                      <Typography variant="body2" mt={1}>
                        工具数量: {server.tools.length}
                      </Typography>
                      {server.tools.length > 0 && (
                        <Box mt={1}>
                          {server.tools.slice(0, 3).map((tool) => (
                            <Chip key={tool} label={tool} size="small" sx={{ mr: 0.5, mb: 0.5 }} />
                          ))}
                          {server.tools.length > 3 && (
                            <Chip label={`+${server.tools.length - 3} 更多`} size="small" />
                          )}
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Grid>

        {/* 工具列表 */}
        <Grid item xs={12} md={4}>
          <Typography variant="subtitle1" gutterBottom>
            可用工具 ({tools.length})
          </Typography>
          <Card>
            <List dense>
              {tools.map((tool, index) => (
                <React.Fragment key={`${tool.serverId}-${tool.name}`}>
                  <ListItem>
                    <ListItemText
                      primary={tool.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" display="block">
                            {tool.description}
                          </Typography>
                          <Typography variant="caption" color="primary">
                            来自: {tool.serverName}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < tools.length - 1 && <Divider />}
                </React.Fragment>
              ))}
              {tools.length === 0 && (
                <ListItem>
                  <ListItemText
                    primary="暂无可用工具"
                    secondary="添加并连接MCP服务器后，工具将显示在这里"
                  />
                </ListItem>
              )}
            </List>
          </Card>
        </Grid>
      </Grid>

      {/* 添加服务器对话框 */}
      <Dialog open={addDialogOpen} onClose={() => setAddDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>添加MCP服务器</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="服务器名称"
              value={newServerConfig.name || ''}
              onChange={(e) => setNewServerConfig({ ...newServerConfig, name: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="服务器URL"
              value={newServerConfig.url || ''}
              onChange={(e) => setNewServerConfig({ ...newServerConfig, url: e.target.value })}
              fullWidth
              required
              placeholder="stdio:command:arg1:arg2 或 http://localhost:3000"
              helperText="stdio传输格式: stdio:command:arg1:arg2，HTTP传输格式: http://host:port"
            />
            <TextField
              label="描述"
              value={newServerConfig.description || ''}
              onChange={(e) => setNewServerConfig({ ...newServerConfig, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={newServerConfig.autoConnect || false}
                  onChange={(e) => setNewServerConfig({ ...newServerConfig, autoConnect: e.target.checked })}
                />
              }
              label="自动连接"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddDialogOpen(false)}>取消</Button>
          <Button onClick={handleAddServer} variant="contained">
            添加
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MCPSettings;
