// MCP 管理器 - 整合本地和远程MCP服务器
import { MCPClient } from './mcp-client';
import { filesystemMCPProxy } from './filesystem-mcp-proxy';
import { mcpBrowserClient, MCPBrowserClient } from './mcp-browser-client';
import { openaiClient, MCPToolCall, MCPToolResult } from './openai-client';
import { databaseManager } from './database';
import {
  MCPServerConfig,
  LocalMCPServer,
  MCPServerInfo,
  MCPToolInfo,
  MCPServerTestResult,
  MCPAvailableTools
} from './types';

export class MCPManager {
  private mcpClient: MCPClient;
  private browserClient: MCPBrowserClient;
  private localServers: Map<string, LocalMCPServer> = new Map();
  private remoteServers: Map<string, MCPServerConfig> = new Map();
  private isBrowserEnvironment: boolean;

  constructor() {
    this.mcpClient = new MCPClient();
    this.browserClient = mcpBrowserClient;
    this.isBrowserEnvironment = MCPBrowserClient.isBrowserEnvironment();
    this.initialize();
  }

  private async initialize(): Promise<void> {
    await this.initializeLocalServers();
    await this.loadRemoteServers();
  }

  private async initializeLocalServers(): Promise<void> {
    if (this.isBrowserEnvironment) {
      // 浏览器环境：使用代理服务器
      const proxyAvailable = await this.browserClient.checkProxyServer();
      if (proxyAvailable) {
        const startResult = await this.browserClient.startFilesystemServer();
        if (startResult.success) {
          this.localServers.set('filesystem', {
            id: 'filesystem',
            name: '文件系统',
            tools: ['search_files', 'read_file', 'list_directory', 'write_file'],
            handler: this.browserClient
          });
          console.log('Browser filesystem MCP server started via proxy');
        } else {
          console.error('Failed to start browser filesystem MCP server:', startResult.error);
        }
      } else {
        console.warn('MCP proxy server not available in browser environment');
      }
    } else {
      // Node.js 环境：直接启动 MCP 服务器
      const startResult = await filesystemMCPProxy.start();
      if (startResult.success) {
        this.localServers.set('filesystem', {
          id: 'filesystem',
          name: '文件系统',
          tools: ['search_files', 'read_file', 'list_directory', 'write_file'],
          handler: filesystemMCPProxy
        });
        console.log('Local filesystem MCP server started');
      } else {
        console.error('Failed to start filesystem MCP server:', startResult.error);
      }
    }

    console.log('Local MCP servers initialized');
  }

  private async loadRemoteServers(): Promise<void> {
    try {
      // 从数据库加载远程MCP服务器配置
      const settings = await databaseManager.getSettingsByCategory('mcp');
      for (const setting of settings) {
        if (setting.key.startsWith('mcp_server_')) {
          try {
            const config = JSON.parse(setting.value) as MCPServerConfig;
            this.remoteServers.set(config.id, config);
            
            // 如果配置为自动连接，则尝试连接
            if (config.autoConnect) {
              await this.connectToRemoteServer(config.id);
            }
          } catch (error) {
            console.error(`Failed to parse MCP server config ${setting.key}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load remote MCP servers:', error);
    }
  }

  // 获取所有MCP服务器信息
  async getAllServers(): Promise<MCPServerInfo[]> {
    const servers: MCPServerInfo[] = [];

    // 添加本地服务器
    for (const [id, server] of this.localServers) {
      servers.push({
        id,
        name: server.name,
        type: 'local',
        status: 'connected', // 本地服务器总是连接状态
        tools: server.tools
      });
    }

    // 添加远程服务器
    const remoteConnections = this.mcpClient.getAllConnections();
    for (const connection of remoteConnections) {
      servers.push({
        id: connection.id,
        name: connection.name,
        type: 'remote',
        status: connection.status as 'connected' | 'disconnected' | 'error',
        tools: connection.tools?.map(tool => tool.name) || [],
        url: connection.url
      });
    }

    return servers;
  }

  // 获取所有可用的工具
  async getAllTools(): Promise<MCPToolInfo[]> {
    const tools: MCPToolInfo[] = [];

    // 添加本地工具
    for (const server of this.localServers.values()) {
      for (const toolName of server.tools) {
        tools.push({
          name: toolName,
          description: `Local tool from ${server.name}`,
          serverId: server.id,
          serverName: server.name
        });
      }
    }

    // 添加远程工具
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        for (const tool of connection.tools) {
          tools.push({
            name: tool.name,
            description: tool.description,
            serverId: connection.id,
            serverName: connection.name
          });
        }
      }
    }

    return tools;
  }

  // 获取可用工具列表（按服务器分组）
  async getAvailableTools(): Promise<MCPAvailableTools[]> {
    const result: MCPAvailableTools[] = [];
    
    // 本地服务器工具
    for (const [id, server] of this.localServers) {
      result.push({
        serverId: id,
        serverName: server.name,
        serverType: 'local',
        tools: server.tools
      });
    }
    
    // 远程服务器工具
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        result.push({
          serverId: connection.id,
          serverName: connection.name,
          serverType: 'remote',
          tools: connection.tools?.map(tool => tool.name) || []
        });
      }
    }
    
    return result;
  }

  // 执行工具调用
  async executeToolCall(toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      // 首先检查本地服务器
      const localServer = this.findLocalServerForTool(toolCall.name);
      if (localServer) {
        return await this.executeLocalToolCall(localServer, toolCall);
      }

      // 然后检查远程服务器
      const remoteServerId = await this.findRemoteServerForTool(toolCall.name);
      if (remoteServerId) {
        return await this.executeRemoteToolCall(remoteServerId, toolCall);
      }

      return {
        success: false,
        error: `No MCP server found for tool: ${toolCall.name}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private findLocalServerForTool(toolName: string): LocalMCPServer | null {
    for (const server of this.localServers.values()) {
      if (server.tools.includes(toolName)) {
        return server;
      }
    }
    return null;
  }

  private async findRemoteServerForTool(toolName: string): Promise<string | null> {
    const connections = this.mcpClient.getAllConnections();
    for (const connection of connections) {
      if (connection.status === 'connected') {
        const tool = connection.tools?.find(t => t.name === toolName);
        if (tool) {
          return connection.id;
        }
      }
    }
    return null;
  }

  private async executeLocalToolCall(server: LocalMCPServer, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      if (server.id === 'filesystem') {
        if (this.isBrowserEnvironment) {
          // 浏览器环境：通过代理服务器调用
          return await this.browserClient.callFilesystemTool(toolCall);
        } else {
          // Node.js 环境：直接调用 MCP 服务器
          const process = filesystemMCPProxy.getProcess();
          if (!process) {
            return {
              success: false,
              error: 'Filesystem MCP server not running'
            };
          }

          // 构造 MCP 请求
          const request = {
            jsonrpc: '2.0',
            id: Date.now(),
            method: 'tools/call',
            params: {
              name: toolCall.name,
              arguments: toolCall.arguments
            }
          };

          // 发送请求到 MCP 服务器
          return new Promise((resolve) => {
            const timeout = setTimeout(() => {
              resolve({
                success: false,
                error: 'Request timeout'
              });
            }, 10000);

            const onData = (data: Buffer) => {
              try {
                const lines = data.toString().split('\n').filter(line => line.trim());
                for (const line of lines) {
                  const response = JSON.parse(line);
                  if (response.id === request.id) {
                    clearTimeout(timeout);
                    process.stdout?.removeListener('data', onData);

                    if (response.error) {
                      resolve({
                        success: false,
                        error: response.error.message
                      });
                    } else {
                      resolve({
                        success: true,
                        result: response.result
                      });
                    }
                    return;
                  }
                }
              } catch (error) {
                // 忽略解析错误
              }
            };

            process.stdout?.on('data', onData);
            process.stdin?.write(JSON.stringify(request) + '\n');
          });
        }
      }

      return {
        success: false,
        error: `Unknown local MCP server: ${server.id}`
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async executeRemoteToolCall(serverId: string, toolCall: MCPToolCall): Promise<MCPToolResult> {
    try {
      const result = await this.mcpClient.callTool(serverId, toolCall.name, toolCall.arguments);
      return {
        success: true,
        result
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 连接到远程MCP服务器
  private async connectToRemoteServer(serverId: string): Promise<boolean> {
    const config = this.remoteServers.get(serverId);
    if (!config) {
      console.error(`Remote server config not found: ${serverId}`);
      return false;
    }

    try {
      await this.mcpClient.connect(config);
      console.log(`Connected to remote MCP server: ${config.name}`);
      return true;
    } catch (error) {
      console.error(`Failed to connect to remote MCP server ${config.name}:`, error);
      return false;
    }
  }

  // 添加远程MCP服务器
  async addRemoteServer(config: MCPServerConfig): Promise<boolean> {
    try {
      // 保存配置
      this.remoteServers.set(config.id, config);

      // 保存到数据库
      await databaseManager.setSetting(`mcp_server_${config.id}`, JSON.stringify(config), 'mcp');

      // 如果设置为自动连接，则立即连接
      if (config.autoConnect) {
        await this.connectToRemoteServer(config.id);
      }

      return true;
    } catch (error) {
      console.error('Failed to add remote MCP server:', error);
      return false;
    }
  }

  // 移除远程MCP服务器
  async removeRemoteServer(serverId: string): Promise<boolean> {
    try {
      // 断开连接
      this.mcpClient.disconnect(serverId);

      // 从内存中移除
      this.remoteServers.delete(serverId);

      // 从数据库中移除
      await databaseManager.setSetting(`mcp_server_${serverId}`, '', 'mcp');

      return true;
    } catch (error) {
      console.error('Failed to remove remote MCP server:', error);
      return false;
    }
  }

  // 测试MCP服务器连接
  async testServerConnection(serverId: string): Promise<MCPServerTestResult> {
    try {
      const config = this.remoteServers.get(serverId);
      if (!config) {
        return { success: false, error: 'Server configuration not found' };
      }

      // 尝试连接
      const connection = await this.mcpClient.connect(config);

      // 获取工具和资源列表
      const tools = connection.tools.map(tool => tool.name);
      const resources = connection.resources.map(resource => resource.uri);

      return {
        success: true,
        tools,
        resources
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // 处理带有工具调用的AI对话
  async handleAIConversationWithTools(
    messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
    sessionId?: string
  ): Promise<{ response: string; toolResults: MCPToolResult[] }> {
    try {
      // 获取所有可用工具
      const availableTools = this.mcpClient.getAllAvailableTools();

      // 发送消息到OpenAI并处理工具调用，附带工具列表
      const { response, toolResults } = await openaiClient.sendMessageWithTools(
        messages,
        (toolCall) => this.executeToolCall(toolCall),
        availableTools
      );

      const assistantResponse = response.choices[0]?.message?.content || '';

      // 保存消息到数据库
      if (sessionId) {
        // 保存用户消息
        const userMessage = messages[messages.length - 1];
        if (userMessage && userMessage.role === 'user') {
          await databaseManager.addChatMessage({
            id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            role: 'user',
            content: userMessage.content,
            sessionId
          });
        }

        // 保存助手回复
        await databaseManager.addChatMessage({
          id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          role: 'assistant',
          content: assistantResponse,
          sessionId
        });
      }

      return {
        response: assistantResponse,
        toolResults
      };
    } catch (error) {
      console.error('AI conversation with tools failed:', error);
      return {
        response: '抱歉，处理您的请求时出现了错误。',
        toolResults: []
      };
    }
  }

  // 关闭所有连接
  async shutdown(): Promise<void> {
    // 断开所有远程连接
    for (const serverId of this.remoteServers.keys()) {
      this.mcpClient.disconnect(serverId);
    }

    // 停止本地 MCP 服务器
    if (this.isBrowserEnvironment) {
      await this.browserClient.stopFilesystemServer();
    } else {
      await filesystemMCPProxy.stop();
    }

    console.log('MCP Manager shutdown complete');
  }
}

// 单例实例
export const mcpManager = new MCPManager();
