// MCP 代理服务器 - 为浏览器环境提供 MCP 服务访问
const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const path = require('path');

const app = express();
const PORT = 3001;

// 存储 MCP 服务器进程
const mcpServers = new Map();

// 中间件
app.use(cors());
app.use(express.json());

// 启动文件系统 MCP 服务器
function startFilesystemMCPServer(config = {}) {
  return new Promise((resolve, reject) => {
    // 启动 MCP 服务器进程
    const serverProcess =  spawn('node', ['-y', '@modelcontextprotocol/server-filesystem', 'D:\\banyunjuhe\\temp'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let initialized = false;

    serverProcess.stderr.on('data', (data) => {
      const message = data.toString();
      console.log('MCP Server:', message);
      if (!initialized && message.includes('connected')) {
        initialized = true;
        resolve(serverProcess);
      }
    });

    serverProcess.on('error', (error) => {
      console.error('MCP Server error:', error);
      if (!initialized) {
        reject(error);
      }
    });

    serverProcess.on('exit', (code) => {
      console.log(`MCP Server exited with code ${code}`);
      mcpServers.delete('filesystem');
    });

    // 超时处理
    setTimeout(() => {
      if (!initialized) {
        resolve(serverProcess); // 即使没有明确的初始化信号也继续
      }
    }, 3000);
  });
}

// API 路由

// 启动文件系统 MCP 服务器
app.post('/api/mcp/filesystem/start', async (req, res) => {
  try {
    if (mcpServers.has('filesystem')) {
      return res.json({ success: true, message: 'Server already running' });
    }

    const config = req.body.config || {};
    const serverProcess = await startFilesystemMCPServer(config);
    mcpServers.set('filesystem', serverProcess);

    res.json({ success: true, message: 'Filesystem MCP server started' });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 停止文件系统 MCP 服务器
app.post('/api/mcp/filesystem/stop', (req, res) => {
  const serverProcess = mcpServers.get('filesystem');
  if (serverProcess) {
    serverProcess.kill();
    mcpServers.delete('filesystem');
    res.json({ success: true, message: 'Filesystem MCP server stopped' });
  } else {
    res.json({ success: false, error: 'Server not running' });
  }
});

// 调用 MCP 工具
app.post('/api/mcp/filesystem/call', async (req, res) => {
  try {
    const serverProcess = mcpServers.get('filesystem');
    if (!serverProcess) {
      return res.status(400).json({ success: false, error: 'Server not running' });
    }

    const { method, params } = req.body;
    
    // 构造 MCP 请求
    const request = {
      jsonrpc: '2.0',
      id: Date.now(),
      method,
      params
    };

    // 发送请求到 MCP 服务器
    serverProcess.stdin.write(JSON.stringify(request) + '\n');

    // 监听响应
    let responseReceived = false;
    const timeout = setTimeout(() => {
      if (!responseReceived) {
        res.status(500).json({ success: false, error: 'Request timeout' });
      }
    }, 10000);

    const onData = (data) => {
      if (responseReceived) return;
      
      try {
        const lines = data.toString().split('\n').filter(line => line.trim());
        for (const line of lines) {
          const response = JSON.parse(line);
          if (response.id === request.id) {
            responseReceived = true;
            clearTimeout(timeout);
            serverProcess.stdout.removeListener('data', onData);
            
            if (response.error) {
              res.status(400).json({ success: false, error: response.error.message });
            } else {
              res.json({ success: true, result: response.result });
            }
            break;
          }
        }
      } catch (error) {
        // 忽略解析错误，可能是部分数据
      }
    };

    serverProcess.stdout.on('data', onData);

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// 获取服务器状态
app.get('/api/mcp/status', (req, res) => {
  const status = {};
  for (const [name, process] of mcpServers) {
    status[name] = {
      running: !process.killed,
      pid: process.pid
    };
  }
  res.json({ success: true, servers: status });
});

// 启动代理服务器
app.listen(PORT, () => {
  console.log(`MCP Proxy Server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /api/mcp/filesystem/start - Start filesystem MCP server');
  console.log('  POST /api/mcp/filesystem/stop - Stop filesystem MCP server');
  console.log('  POST /api/mcp/filesystem/call - Call MCP tool');
  console.log('  GET /api/mcp/status - Get server status');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('Shutting down MCP Proxy Server...');
  for (const [name, process] of mcpServers) {
    console.log(`Stopping ${name} server...`);
    process.kill();
  }
  process.exit(0);
});
