import { app, BrowserWindow, Menu, ipcMain, dialog, shell } from 'electron';
import * as path from 'path';
import { ConfigManager } from '@shared/config-manager';
import { MCPClient } from '@shared/mcp-client';
import { databaseManager } from '@shared/database';
import { openaiClient } from '@shared/openai-client';
import { mcpManager } from '@shared/mcp-manager';
import { Configuration, FileFilter } from '@shared/types';

class AIPlayerApp {
  private mainWindow: BrowserWindow | null = null;
  private configManager: ConfigManager;
  private mcpClient: MCPClient;
  private isDev: boolean;

  constructor() {
    this.isDev = process.argv.includes('--dev');
    this.configManager = new ConfigManager();
    this.mcpClient = new MCPClient();
    
    this.init();
  }

  private async init(): Promise<void> {
    // 等待 Electron 准备就绪
    await app.whenReady();

    // 初始化数据库
    await databaseManager.initialize();

    // 加载配置
    await this.configManager.load();

    // 创建窗口
    this.createWindow();

    // 设置菜单
    this.createMenu();

    // 设置 IPC 处理程序
    this.setupIPCHandlers();

    // 设置应用事件监听器
    this.setupAppEventListeners();
  }

  private createWindow(): void {
    const windowConfig = this.configManager.get('window');
    
    // 创建浏览器窗口
    this.mainWindow = new BrowserWindow({
      width: windowConfig.width || 1200,
      height: windowConfig.height || 800,
      minWidth: windowConfig.minWidth || 800,
      minHeight: windowConfig.minHeight || 600,
      x: windowConfig.x || undefined,
      y: windowConfig.y || undefined,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../renderer/preload.js'),
      },
      icon: path.join(__dirname, '../../assets/icon.png'),
      show: false, // 先不显示，等待ready-to-show事件
      titleBarStyle: 'default',
    });

    // 加载应用的index.html
    if (this.isDev) {
      // 开发模式下加载 Vite 开发服务器
      this.mainWindow.loadURL('http://localhost:3000');
      // 开发模式下打开开发者工具
      this.mainWindow.webContents.openDevTools();
    } else {
      // 生产模式下加载构建后的文件
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // 当窗口准备好显示时显示窗口
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show();
        
        // 恢复窗口状态
        if (windowConfig.maximized) {
          this.mainWindow.maximize();
        }
      }
    });

    // 当窗口关闭时发出
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // 处理外部链接
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // 保存窗口状态
    this.mainWindow.on('resize', () => this.saveWindowState());
    this.mainWindow.on('move', () => this.saveWindowState());
    this.mainWindow.on('maximize', () => this.saveWindowState());
    this.mainWindow.on('unmaximize', () => this.saveWindowState());
  }

  private saveWindowState(): void {
    if (!this.mainWindow) return;

    const bounds = this.mainWindow.getBounds();
    const isMaximized = this.mainWindow.isMaximized();

    this.configManager.set('window.width', bounds.width);
    this.configManager.set('window.height', bounds.height);
    this.configManager.set('window.x', bounds.x);
    this.configManager.set('window.y', bounds.y);
    this.configManager.set('window.maximized', isMaximized);
  }

  private createMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: '文件',
        submenu: [
          {
            label: '打开视频文件',
            accelerator: 'CmdOrCtrl+O',
            click: async () => {
              await this.openFileDialog();
            },
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            },
          },
        ],
      },
      {
        label: '播放',
        submenu: [
          {
            label: '播放/暂停',
            accelerator: 'Space',
            click: () => {
              this.sendToRenderer('toggle-play');
            },
          },
          {
            label: '停止',
            accelerator: 'CmdOrCtrl+S',
            click: () => {
              this.sendToRenderer('stop-video');
            },
          },
        ],
      },
      {
        label: 'AI聊天',
        submenu: [
          {
            label: '打开聊天面板',
            accelerator: 'CmdOrCtrl+T',
            click: () => {
              this.sendToRenderer('toggle-chat-panel');
            },
          },
        ],
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '关于',
            click: async () => {
              if (this.mainWindow) {
                await dialog.showMessageBox(this.mainWindow, {
                  type: 'info',
                  title: '关于 AI Player',
                  message: 'AI Player v1.0.0',
                  detail: '跨平台AI播放器软件\n支持本地视频播放、网络视频播放和AI聊天功能',
                });
              }
            },
          },
        ],
      },
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIPCHandlers(): void {
    // 应用信息
    ipcMain.handle('get-app-version', () => {
      return app.getVersion();
    });

    // 消息框
    ipcMain.handle('show-message-box', async (_, options) => {
      if (this.mainWindow) {
        const result = await dialog.showMessageBox(this.mainWindow, options);
        return result;
      }
      return null;
    });

    // 文件选择
    ipcMain.handle('select-video-file', async () => {
      if (!this.mainWindow) return null;

      const filters: FileFilter[] = [
        { name: '视频文件', extensions: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'] },
        { name: '所有文件', extensions: ['*'] },
      ];

      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openFile'],
        filters,
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        this.configManager.addRecentFile(filePath);
        await this.configManager.save();
        return filePath;
      }

      return null;
    });

    // 设置管理 - 使用数据库
    ipcMain.handle('get-settings', async () => {
      try {
        const settings = await databaseManager.getSettingsByCategory('general');
        const result: Record<string, any> = {};
        settings.forEach(setting => {
          result[setting.key] = setting.value;
        });
        return result;
      } catch (error) {
        console.error('Failed to get settings:', error);
        return this.configManager.getAll(); // 回退到文件配置
      }
    });

    ipcMain.handle('save-settings', async (_, settings: Record<string, any>) => {
      try {
        for (const [key, value] of Object.entries(settings)) {
          await databaseManager.setSetting(key, JSON.stringify(value), 'general');
        }
        return true;
      } catch (error) {
        console.error('Failed to save settings:', error);
        return false;
      }
    });

    // OpenAI 配置管理
    ipcMain.handle('get-openai-config', async () => {
      try {
        return openaiClient.getConfig();
      } catch (error) {
        console.error('Failed to get OpenAI config:', error);
        return null;
      }
    });

    ipcMain.handle('update-openai-config', async (_, config) => {
      try {
        await openaiClient.updateConfig(config);
        return { success: true };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('test-openai-connection', async () => {
      try {
        return await openaiClient.testConnection();
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // AI 聊天相关
    ipcMain.handle('ai-chat', async (_, messages, sessionId) => {
      try {
        const result = await mcpManager.handleAIConversationWithTools(messages, sessionId);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // 聊天会话管理
    ipcMain.handle('create-chat-session', async (_, title) => {
      try {
        const sessionId = await databaseManager.createChatSession(title);
        return { success: true, data: sessionId };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('get-chat-sessions', async () => {
      try {
        const sessions = await databaseManager.getChatSessions();
        return { success: true, data: sessions };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('get-chat-messages', async (_, sessionId, limit) => {
      try {
        const messages = await databaseManager.getChatMessages(sessionId, limit);
        return { success: true, data: messages };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('delete-chat-session', async (_, sessionId) => {
      try {
        await databaseManager.deleteChatSession(sessionId);
        return { success: true };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // 文件搜索相关
    ipcMain.handle('index-files', async (_, paths) => {
      try {
        const result = await mcpManager.initializeFileSearchIndex(paths);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('search-files', async (_, query, options) => {
      try {
        const toolCall = { name: 'file_search', arguments: { query, ...options } };
        const result = await mcpManager.executeToolCall(toolCall);
        return { success: result.success, data: result.result, error: result.error };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // MCP 服务器管理
    ipcMain.handle('get-mcp-servers', async () => {
      try {
        const servers = await mcpManager.getAllServers();
        return { success: true, data: servers };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('add-remote-mcp-server', async (_, config) => {
      try {
        const result = await mcpManager.addRemoteServer(config);
        return { success: result };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('remove-mcp-server', async (_, serverId) => {
      try {
        await mcpManager.removeRemoteServer(serverId);
        return { success: true };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('test-mcp-server', async (_, serverId) => {
      try {
        const result = await mcpManager.testServerConnection(serverId);
        return { success: true, data: result };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('get-mcp-tools', async () => {
      try {
        const tools = await mcpManager.getAllTools();
        return { success: true, data: tools };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    ipcMain.handle('get-available-tools', async () => {
      try {
        const tools = await mcpManager.getAvailableTools();
        return { success: true, data: tools };
      } catch (error) {
        return { success: false, error: (error as Error).message };
      }
    });

    // 窗口控制
    ipcMain.handle('minimize-window', () => {
      if (this.mainWindow) {
        this.mainWindow.minimize();
      }
    });

    ipcMain.handle('maximize-window', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isMaximized()) {
          this.mainWindow.unmaximize();
        } else {
          this.mainWindow.maximize();
        }
      }
    });

    ipcMain.handle('close-window', () => {
      if (this.mainWindow) {
        this.mainWindow.close();
      }
    });
  }

  private setupAppEventListeners(): void {
    app.on('activate', () => {
      // 在macOS上，当单击dock图标并且没有其他窗口打开时，
      // 通常在应用程序中重新创建一个窗口
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    app.on('window-all-closed', () => {
      // 在macOS上，应用程序及其菜单栏通常保持活动状态，
      // 直到用户明确使用Cmd + Q退出
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async () => {
      // 保存配置
      await this.configManager.save();

      // 关闭MCP管理器
      await mcpManager.shutdown();

      // 关闭数据库连接
      await databaseManager.close();
    });
  }

  private async openFileDialog(): Promise<void> {
    if (!this.mainWindow) return;

    const filters: FileFilter[] = [
      { name: '视频文件', extensions: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm'] },
      { name: '所有文件', extensions: ['*'] },
    ];

    const result = await dialog.showOpenDialog(this.mainWindow, {
      properties: ['openFile'],
      filters,
    });

    if (!result.canceled && result.filePaths.length > 0) {
      const filePath = result.filePaths[0];
      this.configManager.addRecentFile(filePath);
      await this.configManager.save();
      this.mainWindow.webContents.send('open-video-file', filePath);
    }
  }

  private sendToRenderer(channel: string, ...args: any[]): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send(channel, ...args);
    }
  }
}

// 创建应用实例
new AIPlayerApp();
