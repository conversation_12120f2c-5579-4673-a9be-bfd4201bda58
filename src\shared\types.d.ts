export interface AppConfig {
    version: string;
    language: string;
    theme: 'light' | 'dark';
    autoUpdate: boolean;
}
export interface PlayerConfig {
    autoplay: boolean;
    volume: number;
    muted: boolean;
    playbackRate: number;
    loop: boolean;
    subtitles: {
        enabled: boolean;
        fontSize: number;
        color: string;
        backgroundColor: string;
    };
}
export interface AIConfig {
    apiUrl: string;
    apiKey: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    chatHistory: {
        enabled: boolean;
        maxMessages: number;
    };
}
export interface MCPServerConfig {
    id: string;
    name: string;
    url: string;
    enabled: boolean;
    autoConnect: boolean;
    auth: {
        type: 'none' | 'basic' | 'bearer';
        username?: string;
        password?: string;
        token?: string;
    };
}

export interface NetworkConfig {
    proxy: {
        enabled: boolean;
        type: 'http' | 'https' | 'socks5';
        host: string;
        port: number;
        username: string;
        password: string;
    };
    timeout: number;
    userAgent: string;
}
export interface WindowConfig {
    width: number;
    height: number;
    minWidth: number;
    minHeight: number;
    x: number | null;
    y: number | null;
    maximized: boolean;
    fullscreen: boolean;
    alwaysOnTop: boolean;
}
export interface ShortcutsConfig {
    togglePlay: string;
    volumeUp: string;
    volumeDown: string;
    seekForward: string;
    seekBackward: string;
    toggleFullscreen: string;
    toggleChat: string;
    openFile: string;
    quit: string;
}
export interface Configuration {
    app: AppConfig;
    player: PlayerConfig;
    ai: AIConfig;
    network: NetworkConfig;
    window: WindowConfig;
    shortcuts: ShortcutsConfig;
    recentFiles: string[];
    bookmarks: string[];
}
export interface IPCMessage<T = any> {
    type: string;
    payload?: T;
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
}
export interface MCPConnection {
    id: string;
    name: string;
    url: string;
    status: 'connecting' | 'connected' | 'disconnected' | 'error';
    capabilities: any;
    tools: MCPTool[];
    resources: MCPResource[];
}
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: any;
}
export interface MCPResource {
    uri: string;
    name: string;
    description?: string;
    mimeType?: string;
}
export interface VideoInfo {
    title: string;
    duration: number;
    currentTime: number;
    volume: number;
    muted: boolean;
    paused: boolean;
    ended: boolean;
    src: string;
}
export interface AppState {
    isReady: boolean;
    currentVideo: VideoInfo | null;
    chatMessages: ChatMessage[];
    mcpConnections: MCPConnection[];
    settings: Configuration;
}
export type AppEvent = 'app-ready' | 'video-loaded' | 'video-play' | 'video-pause' | 'video-ended' | 'chat-message' | 'mcp-connected' | 'mcp-disconnected' | 'settings-changed';
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface FileFilter {
    name: string;
    extensions: string[];
}
