// 数据库管理模块
import * as path from 'path';
import { app } from 'electron';

// 数据库接口定义
export interface DatabaseConfig {
  dbPath: string;
  version: number;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  sessionId?: string;
}

export interface AppSettings {
  id: string;
  key: string;
  value: string;
  category: string;
  updatedAt: Date;
}

export interface FileSearchIndex {
  id: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  content?: string;
  metadata?: string;
  indexedAt: Date;
}

export interface ChatSession {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

// 数据库管理类
export class DatabaseManager {
  private db: any = null;
  private dbPath: string;
  private isInitialized: boolean = false;

  constructor() {
    // 在用户数据目录中创建数据库
    const userDataPath = app.getPath('userData');
    console.log('User Data Path:', userDataPath);
    this.dbPath = path.join(userDataPath, 'ai-player.db');
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 动态导入 better-sqlite3
      const Database = require('better-sqlite3');
      this.db = new Database(this.dbPath);
      
      // 启用外键约束
      this.db.pragma('foreign_keys = ON');
      
      // 创建表
      await this.createTables();
      
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    // 创建设置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS app_settings (
        id TEXT PRIMARY KEY,
        key TEXT NOT NULL UNIQUE,
        value TEXT NOT NULL,
        category TEXT NOT NULL DEFAULT 'general',
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建聊天会话表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS chat_sessions (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        message_count INTEGER DEFAULT 0
      )
    `);

    // 创建聊天消息表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS chat_messages (
        id TEXT PRIMARY KEY,
        session_id TEXT,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES chat_sessions (id) ON DELETE CASCADE
      )
    `);

    // 创建文件搜索索引表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS file_search_index (
        id TEXT PRIMARY KEY,
        file_path TEXT NOT NULL UNIQUE,
        file_name TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT,
        content TEXT,
        metadata TEXT,
        indexed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_chat_messages_session_id ON chat_messages (session_id);
      CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages (timestamp);
      CREATE INDEX IF NOT EXISTS idx_file_search_name ON file_search_index (file_name);
      CREATE INDEX IF NOT EXISTS idx_file_search_content ON file_search_index (content);
      CREATE INDEX IF NOT EXISTS idx_settings_category ON app_settings (category);
    `);

    console.log('Database tables created successfully');
  }

  // 设置管理
  async getSetting(key: string): Promise<string | null> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare('SELECT value FROM app_settings WHERE key = ?');
    const result = stmt.get(key);
    return result ? result.value : null;
  }

  async setSetting(key: string, value: string, category: string = 'general'): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO app_settings (id, key, value, category, updated_at)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    
    const id = `${category}_${key}`;
    stmt.run(id, key, value, category);
  }

  async getSettingsByCategory(category: string): Promise<AppSettings[]> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare('SELECT * FROM app_settings WHERE category = ? ORDER BY key');
    return stmt.all(category);
  }

  // 聊天会话管理
  async createChatSession(title: string): Promise<string> {
    if (!this.isInitialized) await this.initialize();
    
    const id = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const stmt = this.db.prepare(`
      INSERT INTO chat_sessions (id, title, created_at, updated_at)
      VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(id, title);
    return id;
  }

  async getChatSessions(): Promise<ChatSession[]> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare('SELECT * FROM chat_sessions ORDER BY updated_at DESC');
    return stmt.all();
  }

  async updateChatSession(id: string, title?: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    if (title) {
      const stmt = this.db.prepare('UPDATE chat_sessions SET title = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?');
      stmt.run(title, id);
    }
  }

  async deleteChatSession(id: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare('DELETE FROM chat_sessions WHERE id = ?');
    stmt.run(id);
  }

  // 聊天消息管理
  async addChatMessage(message: Omit<ChatMessage, 'timestamp'>): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare(`
      INSERT INTO chat_messages (id, session_id, role, content, timestamp)
      VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(message.id, message.sessionId || null, message.role, message.content);
    
    // 更新会话的消息计数和更新时间
    if (message.sessionId) {
      const updateStmt = this.db.prepare(`
        UPDATE chat_sessions 
        SET message_count = message_count + 1, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `);
      updateStmt.run(message.sessionId);
    }
  }

  async getChatMessages(sessionId?: string, limit: number = 100): Promise<ChatMessage[]> {
    if (!this.isInitialized) await this.initialize();
    
    let stmt;
    if (sessionId) {
      stmt = this.db.prepare('SELECT * FROM chat_messages WHERE session_id = ? ORDER BY timestamp DESC LIMIT ?');
      return stmt.all(sessionId, limit);
    } else {
      stmt = this.db.prepare('SELECT * FROM chat_messages ORDER BY timestamp DESC LIMIT ?');
      return stmt.all(limit);
    }
  }

  async clearChatMessages(sessionId?: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    if (sessionId) {
      const stmt = this.db.prepare('DELETE FROM chat_messages WHERE session_id = ?');
      stmt.run(sessionId);
      
      // 重置会话的消息计数
      const updateStmt = this.db.prepare('UPDATE chat_sessions SET message_count = 0 WHERE id = ?');
      updateStmt.run(sessionId);
    } else {
      const stmt = this.db.prepare('DELETE FROM chat_messages');
      stmt.run();
    }
  }

  // 文件搜索索引管理
  async addFileToIndex(file: Omit<FileSearchIndex, 'id' | 'indexedAt'>): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    const id = `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO file_search_index 
      (id, file_path, file_name, file_size, mime_type, content, metadata, indexed_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
    `);
    
    stmt.run(id, file.filePath, file.fileName, file.fileSize, file.mimeType, file.content, file.metadata);
  }

  async searchFiles(query: string, limit: number = 50): Promise<FileSearchIndex[]> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare(`
      SELECT * FROM file_search_index 
      WHERE file_name LIKE ? OR content LIKE ?
      ORDER BY indexed_at DESC 
      LIMIT ?
    `);
    
    const searchPattern = `%${query}%`;
    return stmt.all(searchPattern, searchPattern, limit);
  }

  async removeFileFromIndex(filePath: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    
    const stmt = this.db.prepare('DELETE FROM file_search_index WHERE file_path = ?');
    stmt.run(filePath);
  }

  // 数据库维护
  async vacuum(): Promise<void> {
    if (!this.isInitialized) await this.initialize();
    this.db.exec('VACUUM');
  }

  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
    }
  }
}

// 单例实例
export const databaseManager = new DatabaseManager();
