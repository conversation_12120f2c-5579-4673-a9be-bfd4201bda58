// 共享类型定义

export interface AppConfig {
  version: string;
  language: string;
  theme: 'light' | 'dark';
  autoUpdate: boolean;
}

export interface PlayerConfig {
  autoplay: boolean;
  volume: number;
  muted: boolean;
  playbackRate: number;
  loop: boolean;
  subtitles: {
    enabled: boolean;
    fontSize: number;
    color: string;
    backgroundColor: string;
  };
}

export interface AIConfig {
  apiUrl: string;
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  chatHistory: {
    enabled: boolean;
    maxMessages: number;
  };
}

export interface MCPServerConfig {
  id: string;
  name: string;
  url: string;
  enabled: boolean;
  autoConnect: boolean;
  auth: {
    type: 'none' | 'basic' | 'bearer';
    username?: string;
    password?: string;
    token?: string;
  };
  description?: string;
  maxFileSize?: number;
  allowedDirectories?: string[];
  excludePatterns?: string[];
}
export interface NetworkConfig {
  proxy: {
    enabled: boolean;
    type: 'http' | 'https' | 'socks5';
    host: string;
    port: number;
    username: string;
    password: string;
  };
  timeout: number;
  userAgent: string;
}

export interface WindowConfig {
  width: number;
  height: number;
  minWidth: number;
  minHeight: number;
  x: number | null;
  y: number | null;
  maximized: boolean;
  fullscreen: boolean;
  alwaysOnTop: boolean;
}

export interface ShortcutsConfig {
  togglePlay: string;
  volumeUp: string;
  volumeDown: string;
  seekForward: string;
  seekBackward: string;
  toggleFullscreen: string;
  toggleChat: string;
  openFile: string;
  quit: string;
}

export interface Configuration {
  app: AppConfig;
  player: PlayerConfig;
  ai: AIConfig;

  network: NetworkConfig;
  window: WindowConfig;
  shortcuts: ShortcutsConfig;
  recentFiles: string[];
  bookmarks: string[];
}

// IPC 消息类型
export interface IPCMessage<T = any> {
  type: string;
  payload?: T;
}

// 聊天消息类型
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

// MCP 相关类型
export interface MCPConnection {
  id: string;
  name: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  capabilities: any;
  tools: MCPTool[];
  resources: MCPResource[];
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: any;
}

export interface MCPResource {
  uri: string;
  name: string;
  description?: string;
  mimeType?: string;
}

// 视频相关类型
export interface VideoInfo {
  title: string;
  duration: number;
  currentTime: number;
  volume: number;
  muted: boolean;
  paused: boolean;
  ended: boolean;
  src: string;
}

// 应用状态类型
export interface AppState {
  isReady: boolean;
  currentVideo: VideoInfo | null;
  chatMessages: ChatMessage[];
  mcpConnections: MCPConnection[];
  settings: Configuration;
}

// 事件类型
export type AppEvent = 
  | 'app-ready'
  | 'video-loaded'
  | 'video-play'
  | 'video-pause'
  | 'video-ended'
  | 'chat-message'
  | 'mcp-connected'
  | 'mcp-disconnected'
  | 'settings-changed';

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 文件过滤器类型
export interface FileFilter {
  name: string;
  extensions: string[];
}

// MCP 管理器相关类型
export interface LocalMCPServer {
  id: string;
  name: string;
  tools: string[];
  handler: any;
}

export interface MCPServerInfo {
  id: string;
  name: string;
  type: 'local' | 'remote';
  status: 'connected' | 'disconnected' | 'error';
  tools: string[];
  url?: string;
}

export interface MCPToolInfo {
  name: string;
  description: string;
  serverId: string;
  serverName: string;
}

export interface MCPServerTestResult {
  success: boolean;
  error?: string;
  tools?: string[];
  resources?: string[];
}

export interface MCPAvailableTools {
  serverId: string;
  serverName: string;
  serverType: 'local' | 'remote';
  tools: string[];
}
