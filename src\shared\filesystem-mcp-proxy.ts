// 文件系统 MCP 代理服务器 - 启动独立的 MCP 服务器进程
import { spawn, ChildProcess } from 'child_process';

export interface FilesystemMCPProxyConfig {
  allowedDirectories?: string[];
  maxFileSize?: number;
  excludePatterns?: string[];
}

export class FilesystemMCPProxy {
  private serverProcess: ChildProcess | null = null;
  private config: FilesystemMCPProxyConfig;

  constructor(config: FilesystemMCPProxyConfig = {}) {
    this.config = {
      allowedDirectories: config.allowedDirectories || [process.cwd()],
      maxFileSize: config.maxFileSize || 10 * 1024 * 1024, // 10MB
      excludePatterns: config.excludePatterns || ['node_modules', '.git', 'dist', 'build']
    };
  }

  /**
   * 启动文件系统 MCP 服务器
   */
  async start(): Promise<{ success: boolean; error?: string }> {
    try {
      if (this.serverProcess) {
        return { success: false, error: 'Server already running' };
      }

      // 启动 MCP 服务器进程
      this.serverProcess = spawn('node', ['-y', '@modelcontextprotocol/server-filesystem', 'D:\\banyunjuhe\\temp'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          MCP_ALLOWED_DIRECTORIES: JSON.stringify(this.config.allowedDirectories),
          MCP_MAX_FILE_SIZE: this.config.maxFileSize?.toString(),
          MCP_EXCLUDE_PATTERNS: JSON.stringify(this.config.excludePatterns)
        }
      });

      // 设置错误处理
      this.serverProcess.on('error', (error) => {
        console.error('Filesystem MCP server error:', error);
      });

      this.serverProcess.on('exit', (code) => {
        console.log(`Filesystem MCP server exited with code ${code}`);
        this.serverProcess = null;
      });

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * 停止文件系统 MCP 服务器
   */
  async stop(): Promise<void> {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
  }

  /**
   * 获取服务器进程
   */
  getProcess(): ChildProcess | null {
    return this.serverProcess;
  }

  /**
   * 检查服务器是否运行
   */
  isRunning(): boolean {
    return this.serverProcess !== null && !this.serverProcess.killed;
  }
}

// 单例实例
export const filesystemMCPProxy = new FilesystemMCPProxy();
