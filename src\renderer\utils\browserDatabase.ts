// 浏览器环境下的数据库适配器，使用localStorage模拟SQLite功能
import {
  ChatMessage,
  AppSettings,
  FileSearchIndex,
  ChatSession
} from '@shared/database';

export class BrowserDatabaseManager {
  private isInitialized = false;
  private storagePrefix = 'ai-player-db';

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 检查localStorage是否可用
      const testKey = `${this.storagePrefix}_test`;
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);

      this.isInitialized = true;
      console.log('Browser database (localStorage) initialized successfully');
    } catch (error) {
      console.error('Failed to initialize localStorage:', error);
      throw error;
    }
  }

  private getStorageKey(category: string, key?: string): string {
    return key ? `${this.storagePrefix}_${category}_${key}` : `${this.storagePrefix}_${category}`;
  }

  private getFromStorage<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('Failed to parse localStorage item:', key, error);
      return defaultValue;
    }
  }

  private setToStorage<T>(key: string, value: T): void {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', key, error);
      throw error;
    }
  }

  // 设置管理
  async getSetting(key: string): Promise<string | null> {
    if (!this.isInitialized) await this.initialize();

    try {
      const storageKey = this.getStorageKey('settings', key);
      const item = localStorage.getItem(storageKey);
      return item;
    } catch (error) {
      console.error('Failed to get setting:', key, error);
      return null;
    }
  }

  async setSetting(key: string, value: string, category: string = 'general'): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      const storageKey = this.getStorageKey('settings', key);
      localStorage.setItem(storageKey, value);

      // 同时保存设置元数据
      const metaKey = this.getStorageKey('settings_meta', key);
      const meta: AppSettings = {
        id: `${category}_${key}`,
        key,
        value,
        category,
        updatedAt: new Date()
      };
      this.setToStorage(metaKey, meta);
    } catch (error) {
      console.error('Failed to set setting:', key, error);
      throw error;
    }
  }

  async getSettingsByCategory(category: string): Promise<AppSettings[]> {
    if (!this.isInitialized) await this.initialize();

    try {
      const settings: AppSettings[] = [];
      const prefix = this.getStorageKey('settings_meta', '');

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(prefix)) {
          const meta = this.getFromStorage<AppSettings>(key, {} as AppSettings);
          if (meta.category === category) {
            settings.push(meta);
          }
        }
      }

      return settings.sort((a, b) => a.key.localeCompare(b.key));
    } catch (error) {
      console.error('Failed to get settings by category:', category, error);
      return [];
    }
  }

  // 聊天会话管理
  async createChatSession(title: string): Promise<string> {
    if (!this.isInitialized) await this.initialize();

    try {
      const id = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const session: ChatSession = {
        id,
        title,
        createdAt: new Date(),
        updatedAt: new Date(),
        messageCount: 0
      };

      const sessionKey = this.getStorageKey('chat_sessions', id);
      this.setToStorage(sessionKey, session);

      // 更新会话列表
      const sessions = await this.getChatSessions();
      const sessionIds = sessions.map(s => s.id);
      if (!sessionIds.includes(id)) {
        sessionIds.push(id);
        const listKey = this.getStorageKey('chat_sessions_list');
        this.setToStorage(listKey, sessionIds);
      }

      return id;
    } catch (error) {
      console.error('Failed to create chat session:', error);
      throw error;
    }
  }

  async getChatSessions(): Promise<ChatSession[]> {
    if (!this.isInitialized) await this.initialize();

    try {
      const listKey = this.getStorageKey('chat_sessions_list');
      const sessionIds = this.getFromStorage<string[]>(listKey, []);

      const sessions: ChatSession[] = [];
      for (const id of sessionIds) {
        const sessionKey = this.getStorageKey('chat_sessions', id);
        const session = this.getFromStorage<ChatSession | null>(sessionKey, null);
        if (session) {
          // 确保日期对象正确
          session.createdAt = new Date(session.createdAt);
          session.updatedAt = new Date(session.updatedAt);
          sessions.push(session);
        }
      }

      // 按更新时间降序排序
      sessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
      return sessions;
    } catch (error) {
      console.error('Failed to get chat sessions:', error);
      return [];
    }
  }

  async updateChatSession(id: string, title?: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      const sessionKey = this.getStorageKey('chat_sessions', id);
      const session = this.getFromStorage<ChatSession | null>(sessionKey, null);

      if (session) {
        if (title) session.title = title;
        session.updatedAt = new Date();
        this.setToStorage(sessionKey, session);
      } else {
        throw new Error('Session not found');
      }
    } catch (error) {
      console.error('Failed to update chat session:', error);
      throw error;
    }
  }

  async deleteChatSession(id: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      // 删除会话
      const sessionKey = this.getStorageKey('chat_sessions', id);
      localStorage.removeItem(sessionKey);

      // 从会话列表中移除
      const listKey = this.getStorageKey('chat_sessions_list');
      const sessionIds = this.getFromStorage<string[]>(listKey, []);
      const updatedIds = sessionIds.filter(sessionId => sessionId !== id);
      this.setToStorage(listKey, updatedIds);

      // 删除相关消息
      await this.clearChatMessages(id);
    } catch (error) {
      console.error('Failed to delete chat session:', error);
      throw error;
    }
  }

  // 聊天消息管理
  async addChatMessage(message: Omit<ChatMessage, 'timestamp'>): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      const chatMessage: ChatMessage = {
        ...message,
        timestamp: new Date()
      };

      // 保存消息
      const messageKey = this.getStorageKey('chat_messages', message.id);
      this.setToStorage(messageKey, chatMessage);

      // 更新消息列表
      if (message.sessionId) {
        const messagesListKey = this.getStorageKey('chat_messages_list', message.sessionId);
        const messageIds = this.getFromStorage<string[]>(messagesListKey, []);
        if (!messageIds.includes(message.id)) {
          messageIds.push(message.id);
          this.setToStorage(messagesListKey, messageIds);
        }

        // 更新会话统计
        const sessionKey = this.getStorageKey('chat_sessions', message.sessionId);
        const session = this.getFromStorage<ChatSession | null>(sessionKey, null);
        if (session) {
          session.messageCount = (session.messageCount || 0) + 1;
          session.updatedAt = new Date();
          this.setToStorage(sessionKey, session);
        }
      }
    } catch (error) {
      console.error('Failed to add chat message:', error);
      throw error;
    }
  }

  async getChatMessages(sessionId?: string, limit: number = 100): Promise<ChatMessage[]> {
    if (!this.isInitialized) await this.initialize();

    try {
      const messages: ChatMessage[] = [];

      if (sessionId) {
        // 获取特定会话的消息
        const messagesListKey = this.getStorageKey('chat_messages_list', sessionId);
        const messageIds = this.getFromStorage<string[]>(messagesListKey, []);

        for (const id of messageIds) {
          const messageKey = this.getStorageKey('chat_messages', id);
          const message = this.getFromStorage<ChatMessage | null>(messageKey, null);
          if (message) {
            // 确保日期对象正确
            message.timestamp = new Date(message.timestamp);
            messages.push(message);
          }
        }
      } else {
        // 获取所有消息
        const prefix = this.getStorageKey('chat_messages', '');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith(prefix)) {
            const message = this.getFromStorage<ChatMessage | null>(key, null);
            if (message) {
              message.timestamp = new Date(message.timestamp);
              messages.push(message);
            }
          }
        }
      }

      // 按时间戳降序排序并限制数量
      messages.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      return messages.slice(0, limit);
    } catch (error) {
      console.error('Failed to get chat messages:', error);
      return [];
    }
  }

  async clearChatMessages(sessionId?: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      if (sessionId) {
        // 清除特定会话的消息
        const messagesListKey = this.getStorageKey('chat_messages_list', sessionId);
        const messageIds = this.getFromStorage<string[]>(messagesListKey, []);

        // 删除所有消息
        for (const id of messageIds) {
          const messageKey = this.getStorageKey('chat_messages', id);
          localStorage.removeItem(messageKey);
        }

        // 清空消息列表
        localStorage.removeItem(messagesListKey);

        // 重置会话消息计数
        const sessionKey = this.getStorageKey('chat_sessions', sessionId);
        const session = this.getFromStorage<ChatSession | null>(sessionKey, null);
        if (session) {
          session.messageCount = 0;
          this.setToStorage(sessionKey, session);
        }
      } else {
        // 清除所有消息
        const prefix = this.getStorageKey('chat_messages', '');
        const keysToRemove: string[] = [];

        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith(prefix) || key.includes('chat_messages_list'))) {
            keysToRemove.push(key);
          }
        }

        keysToRemove.forEach(key => localStorage.removeItem(key));
      }
    } catch (error) {
      console.error('Failed to clear chat messages:', error);
      throw error;
    }
  }

  // 文件搜索索引管理（简化版本，浏览器环境限制）
  async addFileToIndex(file: Omit<FileSearchIndex, 'id' | 'indexedAt'>): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      const fileIndex: FileSearchIndex = {
        id: `file_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        ...file,
        indexedAt: new Date()
      };

      const fileKey = this.getStorageKey('file_search_index', fileIndex.id);
      this.setToStorage(fileKey, fileIndex);

      // 更新文件索引列表
      const listKey = this.getStorageKey('file_search_index_list');
      const fileIds = this.getFromStorage<string[]>(listKey, []);
      if (!fileIds.includes(fileIndex.id)) {
        fileIds.push(fileIndex.id);
        this.setToStorage(listKey, fileIds);
      }
    } catch (error) {
      console.error('Failed to add file to index:', error);
      throw error;
    }
  }

  async searchFiles(query: string, limit: number = 50): Promise<FileSearchIndex[]> {
    if (!this.isInitialized) await this.initialize();

    try {
      const listKey = this.getStorageKey('file_search_index_list');
      const fileIds = this.getFromStorage<string[]>(listKey, []);

      const files: FileSearchIndex[] = [];
      for (const id of fileIds) {
        const fileKey = this.getStorageKey('file_search_index', id);
        const file = this.getFromStorage<FileSearchIndex | null>(fileKey, null);
        if (file) {
          // 确保日期对象正确
          file.indexedAt = new Date(file.indexedAt);
          files.push(file);
        }
      }

      // 简单的文本搜索
      const filteredFiles = files.filter(file =>
        file.fileName.toLowerCase().includes(query.toLowerCase()) ||
        (file.content && file.content.toLowerCase().includes(query.toLowerCase()))
      );

      // 按索引时间降序排序并限制数量
      filteredFiles.sort((a, b) => b.indexedAt.getTime() - a.indexedAt.getTime());
      return filteredFiles.slice(0, limit);
    } catch (error) {
      console.error('Failed to search files:', error);
      return [];
    }
  }

  async removeFileFromIndex(filePath: string): Promise<void> {
    if (!this.isInitialized) await this.initialize();

    try {
      const listKey = this.getStorageKey('file_search_index_list');
      const fileIds = this.getFromStorage<string[]>(listKey, []);

      // 查找要删除的文件
      let fileIdToRemove: string | null = null;
      for (const id of fileIds) {
        const fileKey = this.getStorageKey('file_search_index', id);
        const file = this.getFromStorage<FileSearchIndex | null>(fileKey, null);
        if (file && file.filePath === filePath) {
          fileIdToRemove = id;
          break;
        }
      }

      if (fileIdToRemove) {
        // 删除文件索引
        const fileKey = this.getStorageKey('file_search_index', fileIdToRemove);
        localStorage.removeItem(fileKey);

        // 从列表中移除
        const updatedIds = fileIds.filter(id => id !== fileIdToRemove);
        this.setToStorage(listKey, updatedIds);
      }
    } catch (error) {
      console.error('Failed to remove file from index:', error);
      throw error;
    }
  }

  // 数据库维护
  async vacuum(): Promise<void> {
    // localStorage 不需要手动vacuum，浏览器会自动优化
    console.log('localStorage vacuum not needed, browser handles optimization automatically');
  }

  async close(): Promise<void> {
    // localStorage 不需要关闭连接
    this.isInitialized = false;
    console.log('Browser database (localStorage) closed');
  }
}

// 单例实例
export const browserDatabaseManager = new BrowserDatabaseManager();
