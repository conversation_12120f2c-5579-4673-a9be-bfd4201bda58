import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Toolbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Box,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Typography,
} from '@mui/material';
import {
  FolderOpen,
  Chat,
  Settings,
  PlayArrow,
  Pause,
  Stop,
  ExpandMore,
} from '@mui/icons-material';
import { useAppStore } from '../stores/appStore';

const Toolbar: React.FC = () => {
  const [urlInput, setUrlInput] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const {
    isChatPanelOpen,
    currentVideo,
    toggleChatPanel,
    openSettingsModal,
    loadVideo
  } = useAppStore();

  // 示例视频链接
  const sampleVideos = [
    {
      title: 'Big Buck Bunny (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4'
    },
    {
      title: 'Elephant Dream (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4'
    },
    {
      title: 'For Bigger Blazes (MP4)',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4'
    },
    {
      title: 'Sintel (WebM)',
      url: 'https://upload.wikimedia.org/wikipedia/commons/transcoded/c/c0/Big_Buck_Bunny_4K.webm/Big_Buck_Bunny_4K.webm.480p.vp9.webm'
    }
  ];

  const handleOpenFile = async () => {
    if (typeof window === 'undefined' || !window.electronAPI) {
      console.warn('Electron API 不可用，无法打开文件');
      return;
    }

    try {
      const filePath = await window.electronAPI.selectVideoFile();
      if (filePath) {
        const fileName = filePath.split(/[\\/]/).pop() || '未知文件';
        // 如果是blob URL，直接使用，不添加file://前缀
        if (filePath.startsWith('blob:')) {
          loadVideo(filePath, fileName);
        } else {
          loadVideo(`file://${filePath}`, fileName);
        }
      }
    } catch (error) {
      console.error('打开文件失败:', error);
    }
  };

  const handleLoadUrl = () => {
    if (urlInput.trim()) {
      try {
        const url = new URL(urlInput.trim()); // 验证 URL 格式
        console.log('Loading URL:', url.href);

        // 检查是否是支持的视频格式
        const supportedExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
        const pathname = url.pathname.toLowerCase();

        const isVideoFile = supportedExtensions.some(ext => pathname.endsWith(ext));
        const isImageFile = imageExtensions.some(ext => pathname.endsWith(ext));

        if (isImageFile) {
          const errorMessage = '检测到图片文件，请输入视频文件链接。支持的格式：MP4, WebM, OGG等';
          if (typeof window !== 'undefined' && window.electronAPI) {
            window.electronAPI.showMessageBox({
              type: 'error',
              title: '错误',
              message: errorMessage,
            });
          } else {
            console.error(errorMessage);
            alert(errorMessage);
          }
          return;
        }

        if (isVideoFile || url.protocol === 'blob:' || url.hostname.includes('youtube') || url.hostname.includes('vimeo')) {
          loadVideo(url.href, '网络视频');
          setUrlInput('');
        } else {
          // 对于不确定的文件类型，询问用户是否继续
          const shouldContinue = confirm('无法确定文件类型是否为视频。是否仍要尝试加载？');
          if (shouldContinue) {
            console.warn('User chose to load uncertain file type');
            loadVideo(url.href, '网络视频');
            setUrlInput('');
          }
        }
      } catch (error) {
        const errorMessage = '无效的视频链接格式。请输入有效的URL，例如：https://example.com/video.mp4';
        if (typeof window !== 'undefined' && window.electronAPI) {
          window.electronAPI.showMessageBox({
            type: 'error',
            title: '错误',
            message: errorMessage,
          });
        } else {
          console.error(errorMessage);
          alert(errorMessage);
        }
      }
    }
  };

  const handleSampleVideoClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSampleVideoClose = () => {
    setAnchorEl(null);
  };

  const handleSampleVideoSelect = (url: string, title: string) => {
    setUrlInput(url);
    loadVideo(url, title);
    handleSampleVideoClose();
  };

  const handleUrlKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleLoadUrl();
    }
  };

  const handleTogglePlay = () => {
    const video = document.querySelector('video');
    if (video) {
      if (video.paused) {
        video.play();
      } else {
        video.pause();
      }
    }
  };

  const handleStopVideo = () => {
    const video = document.querySelector('video');
    if (video) {
      video.pause();
      video.currentTime = 0;
    }
  };

  return (
    <AppBar 
      position="static" 
      sx={{ 
        bgcolor: 'background.paper',
        borderBottom: 1,
        borderColor: 'divider',
      }}
    >
      <MuiToolbar variant="dense" sx={{ minHeight: 48 }}>
        {/* 左侧工具 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flex: 1 }}>
          <Button
            variant="contained"
            startIcon={<FolderOpen />}
            onClick={handleOpenFile}
            size="small"
          >
            打开文件
          </Button>

          <TextField
            size="small"
            placeholder="输入网络视频链接..."
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            onKeyDown={handleUrlKeyDown}
            sx={{ 
              width: 300,
              '& .MuiOutlinedInput-root': {
                height: 32,
              },
            }}
          />

          <Button
            variant="outlined"
            onClick={handleLoadUrl}
            size="small"
            disabled={!urlInput.trim()}
          >
            加载
          </Button>

          <Button
            variant="text"
            endIcon={<ExpandMore />}
            onClick={handleSampleVideoClick}
            size="small"
          >
            示例视频
          </Button>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleSampleVideoClose}
          >
            {sampleVideos.map((video, index) => (
              <MenuItem
                key={index}
                onClick={() => handleSampleVideoSelect(video.url, video.title)}
              >
                <Typography variant="body2">{video.title}</Typography>
              </MenuItem>
            ))}
          </Menu>

          {/* 播放控制按钮 */}
          {currentVideo && (
            <Box sx={{ display: 'flex', gap: 0.5, ml: 2 }}>
              <Tooltip title="播放/暂停">
                <IconButton
                  onClick={handleTogglePlay}
                  size="small"
                  sx={{ color: 'primary.main' }}
                >
                  {currentVideo.paused ? <PlayArrow /> : <Pause />}
                </IconButton>
              </Tooltip>
              
              <Tooltip title="停止">
                <IconButton
                  onClick={handleStopVideo}
                  size="small"
                  sx={{ color: 'primary.main' }}
                >
                  <Stop />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        {/* 右侧工具 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button
            variant={isChatPanelOpen ? 'contained' : 'outlined'}
            startIcon={<Chat />}
            onClick={toggleChatPanel}
            size="small"
          >
            {isChatPanelOpen ? '隐藏聊天' : 'AI聊天'}
          </Button>

          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => openSettingsModal(0)}
            size="small"
          >
            设置
          </Button>
        </Box>
      </MuiToolbar>
    </AppBar>
  );
};

export default Toolbar;
