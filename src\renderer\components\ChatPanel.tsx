import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  IconButton
} from '@mui/material';
import {
  Close,
  Send,
  Settings,
} from '@mui/icons-material';
import { useAppStore } from '../stores/appStore';

const ChatPanel: React.FC = () => {
  const [message, setMessage] = useState('');
  const [panelWidth, setPanelWidth] = useState(350); // 默认宽度
  const [isResizing, setIsResizing] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const resizeRef = useRef<HTMLDivElement>(null);
  const {
    chatMessages,
    toggleChatPanel,
    addChatMessage,
    sendAIMessage,
    connectMCP,
    disconnectMCP,
    openSettingsModal
  } = useAppStore();

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // 拖拽调整大小的处理逻辑
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);

    const startX = e.clientX;
    const startWidth = panelWidth;

    const handleMouseMove = (e: MouseEvent) => {
      e.preventDefault();
      const deltaX = startX - e.clientX; // 向左拖拽增加宽度
      const newWidth = Math.max(250, Math.min(800, startWidth + deltaX)); // 限制最小250px，最大800px
      setPanelWidth(newWidth);
    };

    const handleMouseUp = (e: MouseEvent) => {
      e.preventDefault();
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('resizing');

      // 保存宽度到localStorage
      try {
        localStorage.setItem('chatPanelWidth', panelWidth.toString());
      } catch (error) {
        console.warn('Failed to save chat panel width:', error);
      }
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.classList.add('resizing');
  }, [panelWidth]);

  // 从localStorage加载保存的宽度
  useEffect(() => {
    try {
      const savedWidth = localStorage.getItem('chatPanelWidth');
      if (savedWidth) {
        const width = parseInt(savedWidth, 10);
        if (width >= 250 && width <= 800) {
          setPanelWidth(width);
        }
      }
    } catch (error) {
      console.warn('Failed to load chat panel width:', error);
    }
  }, []);

  // 清理事件监听器
  useEffect(() => {
    return () => {
      document.body.classList.remove('resizing');
    };
  }, []);

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    const userMessage = message.trim();
    setMessage('');

    try {
      // 使用新的AI聊天功能
      await sendAIMessage(userMessage);
    } catch (error) {
      console.error('发送消息失败:', error);
      // 添加错误消息
      addChatMessage({
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请稍后再试。',
      });
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleMCPConnect = async () => {
    const serverConfig = {
      id: 'local-server',
      name: '本地MCP服务器',
      url: 'ws://localhost:3001',
      enabled: true,
      autoConnect: true,
      auth: { type: 'none' as const },
    };

    const success = await connectMCP(serverConfig);
    if (success) {
      addChatMessage({
        role: 'system',
        content: 'MCP服务器连接成功！',
      });
    } else {
      addChatMessage({
        role: 'system',
        content: 'MCP服务器连接失败，请检查服务器状态。',
      });
    }
  };

  const handleMCPDisconnect = async (serverId: string) => {
    const success = await disconnectMCP(serverId);
    if (success) {
      addChatMessage({
        role: 'system',
        content: 'MCP服务器已断开连接。',
      });
    }
  };

  const getMessageColor = (role: string) => {
    switch (role) {
      case 'user':
        return 'primary.main';
      case 'assistant':
        return 'background.paper';
      case 'system':
        return 'secondary.main';
      default:
        return 'background.paper';
    }
  };

  const getMessageAlign = (role: string) => {
    return role === 'user' ? 'flex-end' : 'flex-start';
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: panelWidth,
        display: 'flex',
        flexDirection: 'row',
        minWidth: 250,
        maxWidth: 800,
      }}
    >
      {/* 拖拽手柄 */}
      <Box
        ref={resizeRef}
        onMouseDown={handleMouseDown}
        sx={{
          width: 6,
          cursor: 'ew-resize',
          backgroundColor: isResizing ? 'primary.main' : 'transparent',
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: 'rgba(25, 118, 210, 0.1)',
            '& .drag-indicator': {
              opacity: 1,
              color: 'primary.main',
            }
          },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          borderLeft: isResizing ? '2px solid' : '1px solid',
          borderColor: isResizing ? 'primary.main' : 'divider',
        }}
      >
        <Box
          className="drag-indicator"
          sx={{
            width: 2,
            height: 20,
            backgroundColor: 'text.secondary',
            borderRadius: 1,
            opacity: 0.3,
            transition: 'all 0.2s ease',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              left: -2,
              top: 0,
              width: 2,
              height: 20,
              backgroundColor: 'currentColor',
              borderRadius: 1,
            },
            '&::after': {
              content: '""',
              position: 'absolute',
              right: -2,
              top: 0,
              width: 2,
              height: 20,
              backgroundColor: 'currentColor',
              borderRadius: 1,
            }
          }}
        />
      </Box>

      {/* 聊天面板主体 */}
      <Paper
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          borderLeft: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper',
        }}
      >
      {/* 聊天头部 */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
        }}
      >
        <Typography variant="h6">AI 助手</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            onClick={() => {
              openSettingsModal(1); // 打开AI聊天设置标签页
            }}
            size="small"
            title="AI 设置"
          >
            <Settings />
          </IconButton>
          <IconButton onClick={toggleChatPanel} size="small">
            <Close />
          </IconButton>
        </Box>
      </Box>

      {/* 消息列表 */}
      <Box
        sx={{
          flex: 1,
          overflow: 'auto',
          p: 1,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
        }}
      >
        {chatMessages.map((msg) => (
          <Box
            key={msg.id}
            sx={{
              display: 'flex',
              justifyContent: getMessageAlign(msg.role),
            }}
          >
            <Paper
              sx={{
                p: 1.5,
                maxWidth: '80%',
                bgcolor: getMessageColor(msg.role),
                color: msg.role === 'user' ? 'primary.contrastText' : 'text.primary',
              }}
            >
              <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                {msg.content}
              </Typography>
            </Paper>
          </Box>
        ))}
        <div ref={messagesEndRef} />
      </Box>

      {/* 消息输入 */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder="输入消息..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            size="small"
          />
          <Button
            variant="contained"
            onClick={handleSendMessage}
            disabled={!message.trim()}
            sx={{ minWidth: 'auto', px: 2 }}
          >
            <Send />
          </Button>
        </Box>
      </Box>
    </Paper>
    </Box>
  );
};

export default ChatPanel;
